{"name": "JUTPSInputControlls", "maps": [{"name": "Player", "id": "a0ae23dc-5c67-45d9-aa9d-86252c3e29ed", "actions": [{"name": "Move", "type": "Value", "id": "1af53fb5-c3f1-4dd9-885d-36b2d48cb125", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "1159d54a-8fff-471f-98c6-5e9f84ae0a87", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "2e0a2b0c-040c-4c24-90cf-b562c9aaab28", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Fire", "type": "<PERSON><PERSON>", "id": "063a1f26-d998-4f20-a2ef-b58ca11b97f6", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Aim", "type": "<PERSON><PERSON>", "id": "0fe379eb-95b9-42c9-b8ce-c165f2e234d1", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Run", "type": "<PERSON><PERSON>", "id": "43dff8ee-059e-4507-a5d4-af99ddb26ad9", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Punch", "type": "<PERSON><PERSON>", "id": "5f047d5e-39c4-44e6-87e0-fed3d9ec84cf", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Roll", "type": "<PERSON><PERSON>", "id": "9fef2ff8-82a6-4f02-923a-6c1247f8b315", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Prone", "type": "PassThrough", "id": "d8a90877-774e-4da6-b1fd-9cbf561ac7e7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "d01232a0-e5cf-472f-bc24-dbeb27faa604", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Reload", "type": "<PERSON><PERSON>", "id": "550f5b6c-c6e1-43c9-a6f7-ddd9bfbea7cf", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "c3ad9ef6-a316-4ad5-872a-b858b5113526", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Pickup", "type": "<PERSON><PERSON>", "id": "c64e5fc8-3955-473f-ba3c-27d58fb79fc5", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Open Inventory", "type": "<PERSON><PERSON>", "id": "cb8b467b-7be1-4fbe-aced-26c48cb470f5", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Next", "type": "<PERSON><PERSON>", "id": "10e867c8-9219-49c6-834f-27cd04dbae6e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Previous", "type": "<PERSON><PERSON>", "id": "8f361267-a454-4933-9d26-43a75045d8cb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Mouse Position", "type": "Value", "id": "c5027329-b8f0-4b9c-aab3-29f32248c0f6", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Slot1", "type": "<PERSON><PERSON>", "id": "3a8fa994-9689-468c-b9b2-a56a62be1009", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot2", "type": "<PERSON><PERSON>", "id": "5b3b77b4-c751-4746-b875-621600be6fa3", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot3", "type": "<PERSON><PERSON>", "id": "2e63e1b3-e963-41ad-89d5-71a985b750cf", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot4", "type": "<PERSON><PERSON>", "id": "f8c8567c-b5b0-47d7-977c-439703445940", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot5", "type": "<PERSON><PERSON>", "id": "be1a5f0a-90ff-4dc9-9cc5-d4484610b6a3", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot6", "type": "<PERSON><PERSON>", "id": "71646eb1-67bc-40b6-a003-486f1ba4bcf7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot7", "type": "<PERSON><PERSON>", "id": "9d1226da-79de-49c9-abfc-c235994a5bd0", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot8", "type": "<PERSON><PERSON>", "id": "49b6634a-6e85-41b9-94a2-9da690729edb", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot9", "type": "<PERSON><PERSON>", "id": "283a8134-f860-486f-8d9c-86b01afae96f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Slot10", "type": "<PERSON><PERSON>", "id": "7747d8b0-72ae-4d36-997f-8ed9e5ee10f7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "978bfe49-cc26-4a3d-ab7b-7d7a29327403", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "StickDeadzone(max=1)", "groups": ";Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "WASD", "id": "00ca640b-d935-4593-8157-c05846ea39b3", "path": "Dpad", "interactions": "", "processors": "StickDeadzone(max=1)", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "e2062cb9-1b15-46a2-838c-2f8d72a0bdd9", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "8180e8bd-4097-4f4e-ab88-4523101a6ce9", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "320bffee-a40b-4347-ac70-c210eb8bc73a", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "1c5327b5-f71c-4f60-99c7-4e737386f1d1", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "d2581a9b-1d11-4566-b27d-b92aff5fabbc", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "2e46982e-44cc-431b-9f0b-c11910bf467a", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "fcfe95b8-67b9-4526-84b5-5d0bc98d6400", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "77bff152-3580-4b21-b6de-dcd0c7e41164", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "1635d3fe-58b6-4ba9-a4e2-f4b964f6b5c8", "path": "<XRController>/{Primary2DAxis}", "interactions": "", "processors": "StickDeadzone(max=1)", "groups": "XR", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3ea4d645-4504-4529-b061-ab81934c3752", "path": "<Joystick>/stick", "interactions": "", "processors": "StickDeadzone(max=0.925)", "groups": "Joystick", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8c8e490b-c610-4785-884f-f04217b23ca4", "path": "<Pointer>/delta", "interactions": "", "processors": "ScaleVector2(x=0.1,y=0.05)", "groups": "Keyboard&Mouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "143bb1cd-cc10-4eca-a2f0-a3664166fe91", "path": "<Gamepad>/rightTrigger", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "05f6913d-c316-48b2-a6bb-e225f14c7960", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ee3d0cd2-254e-47a7-a8cb-bc94d9658c54", "path": "<Joystick>/trigger", "interactions": "", "processors": "", "groups": "", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "15e7598f-0be5-401f-ba62-d73688ffd6d4", "path": "<Keyboard>/ctrl", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Roll", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9314414d-1863-4870-a58f-c88f145ff9ac", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "", "action": "Roll", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b004e5a1-8718-488a-ac2c-ecb4869bb379", "path": "<Keyboard>/z", "interactions": "Press", "processors": "", "groups": "Keyboard&Mouse", "action": "Prone", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3bc6585a-8a4f-4148-95ff-48e23442f5a9", "path": "<Gamepad>/dpad/down", "interactions": "Hold", "processors": "", "groups": "Gamepad", "action": "Prone", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "582d0972-ef75-4ed6-b02d-b9b3bd0fd5bd", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "85263253-7c28-49d6-b635-e30de38c2e35", "path": "<Gamepad>/dpad/right", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d9220014-5c73-49d9-89e7-1df1713b2f9c", "path": "<Gamepad>/leftStickPress", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "98b8ec44-dab3-4568-9d0d-30ee13e2de9a", "path": "<Keyboard>/shift", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Run", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "3937fa9f-e0e8-4b81-95df-0cf78f8a2b04", "path": "<Gamepad>/leftTrigger", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Aim", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "faafef29-a726-4bfd-8a8e-39903fe003f6", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Aim", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b5cad117-7102-4851-ace6-623948d8821a", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "57f0506a-d74f-40e0-bf2f-57f96752a816", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4e88c8cb-397c-49c5-934f-c065b242d94d", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "ScaleVector2(y=0.6)", "groups": "Joystick", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e12f5761-ce34-4374-9f36-50a073244524", "path": "<Gamepad>/dpad/down", "interactions": "", "processors": "", "groups": "Gamepad", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8861427a-6783-4fd9-aa51-bb6b42119c8a", "path": "<Keyboard>/c", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bb2e1b62-f2da-4e99-ae9d-dce1c14fb686", "path": "<Gamepad>/dpad/up", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Open Inventory", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "81837eca-ed82-4ea8-b91f-38b756f50fde", "path": "<Keyboard>/tab", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Open Inventory", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2399d236-ae65-4246-bffe-7f663ecefc24", "path": "<Keyboard>/r", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Reload", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "49ae7e10-899d-4c08-bf59-3d05f1b90d12", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Reload", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0aaffed9-1326-42fb-af8f-85bc87db9d42", "path": "<Keyboard>/f", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7c54eaf6-da0a-4a1c-a211-d062cf51a94d", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "ee131511-dfd4-4c7f-ba48-ba10bdee46b4", "path": "<Keyboard>/f", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Pickup", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bc75d438-efd5-4482-98bc-93bd833eae42", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Pickup", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "eb65d9b1-7224-45d8-bbba-70dffba128ed", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Mouse Position", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2c61d3cb-499a-4d99-aa46-f39b322ec9ea", "path": "<Keyboard>/q", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f361e82c-d17c-46a8-8b94-0770971476a9", "path": "<Gamepad>/dpad/left", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9c9b9a8b-227a-43de-a1cc-7c6df536e553", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Punch", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6f40e7f8-5aeb-49ab-8ccf-d4f5a6317a40", "path": "<Gamepad>/buttonNorth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Punch", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e33cee41-cbb5-4335-a045-9b6d09b9663b", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot1", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "383dfe37-bb08-4fbb-8776-83c6c1fd097e", "path": "<Keyboard>/2", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot2", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "0e8cf112-f918-4c30-b8fd-778735bab97c", "path": "<Keyboard>/3", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot3", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c2900dbe-19c8-4f2f-9582-dc4420ed7ed8", "path": "<Keyboard>/4", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot4", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9dbd0cce-ca21-459a-bf72-454361263029", "path": "<Keyboard>/5", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot5", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "03a9d4a5-96a4-4fa0-a81e-55234b0ef25a", "path": "<Keyboard>/6", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot6", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a55a90b4-662a-47f6-87c1-64508c53ad21", "path": "<Keyboard>/7", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot7", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b3408c46-9a72-4a21-b4e9-ff1298d0b949", "path": "<Keyboard>/8", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot8", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "1312fe50-0cfa-424d-9494-78bca16b3666", "path": "<Keyboard>/9", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot9", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "68b6b30a-79a0-49f0-82ee-c4bf25db758c", "path": "<Keyboard>/0", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Slot10", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "de720d52-7ecb-445a-9bbe-1e80559bca8b", "actions": [{"name": "Navigate", "type": "Value", "id": "25b7f957-93a6-474b-9684-29b3e1ad7784", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "7fcd0736-3054-4c6c-a227-d8fd423dad40", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "205ed331-a95a-4d1b-b3f8-7bb9ee51c6d5", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "d9b10c3a-1698-4838-951f-6e965445abc8", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Click", "type": "PassThrough", "id": "753b30ee-302e-4144-a15d-ca44dd678594", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ScrollWheel", "type": "PassThrough", "id": "e9c66422-6426-4cda-870f-25c78f6cffad", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "a2988bcf-8134-46bc-b6c8-8c1c90fef2f3", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "RightClick", "type": "PassThrough", "id": "0b22c352-47f4-47d5-9eb9-e129e1f55d89", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "ce4419a5-4aef-49a8-878a-2dc5679c9a5c", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "2445883b-1464-4fdf-ac97-01c3460be12b", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Gamepad", "id": "809f371f-c5e2-4e7a-83a1-d867598f40dd", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "14a5d6e8-4aaf-4119-a9ef-34b8c2c548bf", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "9144cbe6-05e1-4687-a6d7-24f99d23dd81", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2db08d65-c5fb-421b-983f-c71163608d67", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "58748904-2ea9-4a80-8579-b500e6a76df8", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "8ba04515-75aa-45de-966d-393d9bbd1c14", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "712e721c-bdfb-4b23-a86c-a0d9fcfea921", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "fcd248ae-a788-4676-a12e-f4d81205600b", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "1f04d9bc-c50b-41a1-bfcc-afb75475ec20", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "fb8277d4-c5cd-4663-9dc7-ee3f0b506d90", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "e25d9774-381c-4a61-b47c-7b6b299ad9f9", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "3db53b26-6601-41be-9887-63ac74e79d19", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "0cb3e13e-3d90-4178-8ae6-d9c5501d653f", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "0392d399-f6dd-4c82-8062-c1e9c0d34835", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "942a66d9-d42f-43d6-8d70-ecb4ba5363bc", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "ff527021-f211-4c02-933e-5976594c46ed", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "563fbfdd-0f09-408d-aa75-8642c4f08ef0", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "eb480147-c587-4a33-85ed-eb0ab9942c43", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "2bf42165-60bc-42ca-8072-8c13ab40239b", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "85d264ad-e0a0-4565-b7ff-1a37edde51ac", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "74214943-c580-44e4-98eb-ad7eebe17902", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "cea9b045-a000-445b-95b8-0c171af70a3b", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8607c725-d935-4808-84b1-8354e29bab63", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "4cda81dc-9edd-4e03-9d7c-a71a14345d0b", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "9e92bb26-7e3b-4ec4-b06b-3c8f8e498ddc", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "82627dcc-3b13-4ba9-841d-e4b746d6553e", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c52c8e0b-8179-41d3-b8a1-d149033bbe86", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e1394cbc-336e-44ce-9ea8-6007ed6193f7", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5693e57a-238a-46ed-b5ae-e64e6e574302", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4faf7dc9-b979-4210-aa8c-e808e1ef89f5", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8d66d5ba-88d7-48e6-b1cd-198bbfef7ace", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "47c2a644-3ebc-4dae-a106-589b7ca75b59", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bb9e6b34-44bf-4381-ac63-5aa15d19f677", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "38c99815-14ea-4617-8627-164d27641299", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "24066f69-da47-44f3-a07e-0015fb02eb2e", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4c191405-5738-4d4b-a523-c6a301dbf754", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7236c0d9-6ca3-47cf-a6ee-a97f5b59ea77", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "23e01e3a-f935-4948-8d8b-9bcac77714fb", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}, {"name": "PlayerTest", "id": "bb65a981-da2d-4617-b9a2-0160f24c2faa", "actions": [{"name": "Shoot", "type": "<PERSON><PERSON>", "id": "f13e32b0-e9f8-49e9-be6b-a994062cf146", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "SwitchCharacter", "type": "<PERSON><PERSON>", "id": "ee2b914f-ce65-4353-840a-79505542cbf6", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Update<PERSON>ay<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "90b29dd8-ba31-4731-851a-1c594b9c49cc", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Move", "type": "Value", "id": "943ecf22-43fb-484a-8d00-7f742f4338f4", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "PointerPosition", "type": "Value", "id": "f28b038d-c09c-42e5-937c-24ad421d2616", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "TestClick", "type": "<PERSON><PERSON>", "id": "11196d11-9cff-4776-ad06-fc267372a832", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Fire", "type": "<PERSON><PERSON>", "id": "03c85472-b85d-4353-ac90-7c569e6640ac", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Value", "id": "ffe665c4-9f2c-4490-92fc-a2ddb6badbe0", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "TestCrouch", "type": "<PERSON><PERSON>", "id": "8254220f-91b3-4a36-a945-ddca95a2d9f3", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TestProne", "type": "<PERSON><PERSON>", "id": "49b9fa65-650e-47cd-8ac8-fe81c57b242e", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "SpaceButton", "type": "<PERSON><PERSON>", "id": "2ef5764f-a8fb-4f6b-afc4-0234092f5f24", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "", "id": "71117692-a6df-4ff4-a13d-22f67a7ea47e", "path": "<Keyboard>/p", "interactions": "", "processors": "", "groups": "", "action": "Shoot", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f925381a-9ba4-4700-b42f-9c9a9c29024d", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "", "action": "SwitchCharacter", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "990014a1-fcf4-40ed-962d-fd85043fa40b", "path": "<Keyboard>/l", "interactions": "", "processors": "", "groups": "", "action": "Update<PERSON>ay<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "2D Vector", "id": "bdc7f7e9-2169-49d2-8ee1-1e7df841dd3c", "path": "2DVector", "interactions": "", "processors": "StickDeadzone", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "c0fd88ec-9361-4a61-9a2a-78bacdccdc58", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "e326dcb3-6c61-49db-9507-ffaf9e6b129f", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "88712e55-cb22-4a22-9202-12c6191cf24a", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "8a582ad3-2649-4bfc-a7f4-14b71b7fa39e", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "6fd024c4-3830-49d0-bae2-39ca0fd7910e", "path": "<Pointer>/position", "interactions": "", "processors": "", "groups": "", "action": "PointerPosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "da067110-b271-4ab9-9e5f-b72a8a74062c", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "TestClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a9fc9bff-08c7-457f-a29d-dca76ce49bf3", "path": "<Touchscreen>/Press", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "TestClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "cbc1195a-64ec-4fa4-a0e0-340cfa8ee182", "path": "<Keyboard>/m", "interactions": "", "processors": "", "groups": "", "action": "Fire", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "becc6399-7d25-41e0-9691-0e42a67d6cfa", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "<PERSON><PERSON><PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "bb5d90be-d4a7-4fb3-910a-5741fbd69a86", "path": "<Touchscreen>/primaryTouch/delta", "interactions": "", "processors": "", "groups": "", "action": "<PERSON><PERSON><PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8b070a35-e3a9-4447-ac7f-498512902cb7", "path": "<Keyboard>/b", "interactions": "", "processors": "", "groups": "", "action": "TestCrouch", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2200f35f-ef3c-44d1-9c1c-2509205aa471", "path": "<Keyboard>/n", "interactions": "", "processors": "", "groups": "", "action": "TestProne", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e79d9033-2727-4e9d-8ab0-8dfe696ffb7f", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "", "action": "SpaceButton", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}