﻿using UnityEngine;

namespace GameLogic
{
    public class RoleVisionSupport : MonoBehaviour
    {
        private Material _thisMaterial;

        private void Start()
        {
            _thisMaterial = GetComponent<Renderer>().sharedMaterial;
        }

        public void SetRimLight(bool isOn)
        {
            if (_thisMaterial == null)
            {
                Debug.Log("RoleVisionSupport: 材质为空");
                return;
            }

            _thisMaterial.SetFloat("_RimLight", isOn ? 1f : 0f);
            if (isOn)
                _thisMaterial.EnableKeyword("_RIMLIGHT");
            else
                _thisMaterial.DisableKeyword("_RIMLIGHT");
        }
    }
}