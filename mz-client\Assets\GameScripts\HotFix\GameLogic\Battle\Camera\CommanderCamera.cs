﻿using Cinemachine;
using TEngine;
using UnityEngine;
using UnityEngine.InputSystem;

namespace GameLogic
{
    public class CommanderCamera : MonoBehaviour
    {
        [Header("目标对象")] public Transform targetPlayer;

        [Header("相机偏移参数")] public float distance = 60f; // 摄像机与目标距离
        public float upOffset = 14f; // 摄像机在目标上方的偏移

        [Header("遮挡检测")] public LayerMask obstacleLayers;
        public float cameraCollisionRadius = 0.3f;

        [Header("旋转控制")] public float sensitivity = 0.1f;
        public float _yaw = 0.7f;
        public float _pitch = -8f; // 初始仰角
        public float maxPitch = 85f;

        private bool _isTouchDragging;
        private bool _isLocking;

        private CinemachineVirtualCamera _virtualCamera;

        private void Start()
        {
            _virtualCamera = GetComponent<CinemachineVirtualCamera>();

            GameEvent.AddEventListener<bool>(GameCommon.ActorEventDefine.TestTacticalSupportState, ChangeTactSystemState);
            GameEvent.AddEventListener<bool>(GameCommon.ActorEventDefine.BattleCameraLock, SetCameraLockState);
            GameEvent.AddEventListener<BattleCameraMode>(GameCommon.ActorEventDefine.BattleSetCameraMode, SetCameraMode);
        }

        private void SetCameraLockState(bool state) => _isLocking = state;

        private void Update()
        {
            if (targetPlayer == null || _isLocking) return;

            if (_cameraMode == BattleCameraMode.Commander)
            {
                UpdateCameraPosition();
            }
            else if (_cameraMode == BattleCameraMode.Tps)
            {
                UpdateTpsCameraPosition();
            }
            else if (_cameraMode == BattleCameraMode.Fps)
            {
                UpdateFpsCamera();
            }
        }

        public void OnTouchDragDelta(Vector2 delta)
        {
            if (!_isTouchDragging) return;

            _yaw += delta.x * sensitivity;
            _pitch += delta.y * sensitivity;
            _pitch = Mathf.Clamp(_pitch, -maxPitch, maxPitch);
        }

        private void UpdateCameraPosition()
        {
            // 遮挡检测
            var origin = targetPlayer.position + Vector3.up * upOffset;

            // 动态调整观察点高度
            float currentDistance = Vector3.Distance(transform.position, origin);
            var adjustedLookOffset = Vector3.up * Mathf.Lerp(1f, upOffset, currentDistance / distance);

            var lookTarget = targetPlayer.position + adjustedLookOffset;

            transform.LookAt(lookTarget);

            // 设置摄像机位置与朝向
            transform.position = GetCameraNewPosition();
            transform.LookAt(lookTarget);
        }

        private Vector3 GetCameraNewPosition()
        {
            // 计算方向
            var pitchRad = Mathf.Deg2Rad * _pitch;
            var yawRad = Mathf.Deg2Rad * _yaw;

            var direction = new Vector3(
                Mathf.Sin(yawRad) * Mathf.Cos(pitchRad),
                Mathf.Sin(pitchRad),
                Mathf.Cos(yawRad) * Mathf.Cos(pitchRad)
            );

            var offset = direction * distance;
            var desiredPosition = targetPlayer.position + Vector3.up * upOffset - offset;

            // 遮挡检测
            var origin = targetPlayer.position + Vector3.up * upOffset;
            var rayDir = -offset.normalized;

            if (Physics.SphereCast(origin, cameraCollisionRadius, rayDir, out var hit, distance, obstacleLayers))
            {
                var targetDistance = Mathf.Max(hit.distance - 0.1f, 0.5f);
                desiredPosition = origin + rayDir * targetDistance;
            }

            return desiredPosition;
        }


        public void ChangeCameraLookAt(Transform target)
        {
            targetPlayer = target;
            transform.LookAt(target);
        }

        public void SetTouchDragState(bool state)
        {
            _isTouchDragging = state;
        }

        private GameObject _supportCache;

        private void ChangeTactSystemState(bool state)
        {
            if (state)
            {
                _supportCache = new GameObject("SupportCache");
                _supportCache.AddComponent<SupportPreviewMover>();
                _supportCache.transform.position = targetPlayer.position;
                targetPlayer = _supportCache.transform;
            }
            else
            {
                targetPlayer = BattleSystem.Instance.GetLeader().MainBody;
                Destroy(_supportCache);
                _supportCache = null;
            }

            GameEvent.Send(GameCommon.ActorEventDefine.TestSetLeaderMoveState, state ? LeaderMoveState.Stop : LeaderMoveState.NormalMove);
        }

        public Vector3 GetTargetPosition()
        {
            return _supportCache.transform.position;
        }

        private BattleCameraMode _cameraMode = BattleCameraMode.Commander;

        private void SetCameraMode(BattleCameraMode expression)
        {
            switch (expression)
            {
                case BattleCameraMode.Commander:
                    distance = 93f;
                    upOffset = 12f;
                    _virtualCamera.m_Lens.FieldOfView = 25f;
                    sensitivity = 0.1f;
                    Debug.Log("相机切换到指挥视角");
                    break;
                case BattleCameraMode.Tps:
                    InitTpsCamera();
                    Debug.Log("相机切换到TPS视角");
                    break;
                case BattleCameraMode.Fps:
                    Debug.Log("相机切换到FPS视角");
                    InitFpsCamera();
                    break;
                default:
                    Debug.Log("该状态未定义");
                    break;
            }

            _cameraMode = expression;
        }

        public float yawDesc = 7f;
        public float pitchDesc = 8f;

        private void InitTpsCamera()
        {
            distance = 20f;
            upOffset = 4.5f;
            // var worldTargetPoint = GetLookAtPoint();
            // SetYawPitchToLookAt(worldTargetPoint);

            _yaw -= yawDesc;
            _pitch -= pitchDesc;
            _virtualCamera.m_Lens.FieldOfView = 25f;
            sensitivity = 0.1f;
        }

        /// <summary>
        /// 设置摄像机角度，使得从 TPS 视角下，摄像机依然看向原先的目标点（如 FPS 模式下的射线命中点）
        /// </summary>
        /// <param name="worldTargetPoint">原 FPS 模式下摄像机命中的 hit.point</param>
        private void SetYawPitchToLookAt(Vector3 worldTargetPoint)
        {
            // TPS 摄像机从 targetPlayer 的这个偏移点出发
            Vector3 pivot = targetPlayer.position + GetCameraRightDirection(_yaw) * shoulderOffsetX + Vector3.up * shoulderOffsetY;

            // 计算方向向量
            Vector3 dir = (worldTargetPoint - pivot).normalized;

            // 推导出 yaw / pitch（注意 pitch 是反的）
            _pitch = Mathf.Asin(dir.y) * Mathf.Rad2Deg;
            _yaw = Mathf.Atan2(dir.x, dir.z) * Mathf.Rad2Deg;
        }


        /// <summary>
        /// 基于当前 yaw 计算右方向（世界空间下）
        /// </summary>
        private Vector3 GetCameraRightDirection(float yaw)
        {
            // 注意：yaw 是角度值
            float yawRad = yaw * Mathf.Deg2Rad;
            Vector3 forward = new Vector3(Mathf.Sin(yawRad), 0, Mathf.Cos(yawRad));
            return Vector3.Cross(Vector3.up, forward).normalized;
        }


        private void OnDestroy()
        {
            GameEvent.RemoveEventListener<bool>(GameCommon.ActorEventDefine.TestTacticalSupportState, ChangeTactSystemState);
            GameEvent.RemoveEventListener<bool>(GameCommon.ActorEventDefine.BattleCameraLock, SetCameraLockState);
            GameEvent.RemoveEventListener<BattleCameraMode>(GameCommon.ActorEventDefine.BattleSetCameraMode, SetCameraMode);
        }

        #region TPS设置

        [Header("TPS 视角偏移（角色肩膀偏移）")] public float shoulderOffsetX = 2.1f; // 控制角色保持在画面偏左或偏右，0.5是偏右肩（大多数 TPS 使用右肩）
        public float shoulderOffsetY = 6.6f; // 控制摄像机视线高度，通常为角色头部或眼睛高度


        /// <summary>
        /// 使用 yaw/pitch 控制摄像机，并根据当前视角动态确定角色右肩偏移点，使人物始终位于屏幕固定位置
        /// </summary>
        private void UpdateTpsCameraPosition()
        {
            if (targetPlayer == null) return;

            // 计算当前视角方向（球坐标 -> 笛卡尔）
            var pitchRad = Mathf.Deg2Rad * _pitch;
            var yawRad = Mathf.Deg2Rad * _yaw;

            var viewDir = new Vector3(
                Mathf.Sin(yawRad) * Mathf.Cos(pitchRad),
                Mathf.Sin(pitchRad),
                Mathf.Cos(yawRad) * Mathf.Cos(pitchRad)
            );

            // 💡 关键：计算当前视角下的右方向（由视角而非角色提供）
            Vector3 cameraRight = Vector3.Cross(Vector3.up, viewDir).normalized;
            Vector3 lookTarget = targetPlayer.position
                                 + cameraRight * shoulderOffsetX // 当前视角下的右方向
                                 + Vector3.up * shoulderOffsetY; // 高度偏移

            transform.position = GetCameraNewPosition();
            transform.LookAt(lookTarget);
        }

        #endregion

        #region FPS模式

        [Header("FPS模式参数")] public float fpsForwardOffset = 3.1f; // 相机在角色前方的距离
        public float fpsUpOffset = 4.1f; // 相机在角色头部高度

        /// <summary>
        /// 初始化 FPS 摄像机：以当前摄像机看的方向为基准继续朝向
        /// </summary>
        private void InitFpsCamera()
        {
            if (targetPlayer == null) return;

            Vector3 upOffsetVec = Vector3.up * fpsUpOffset;
            Vector3 forwardOffsetVec = transform.rotation * Vector3.forward * fpsForwardOffset;

            var tempPos = targetPlayer.position + upOffsetVec + forwardOffsetVec;

            // 当前摄像机看的方向：从相机位置看向目标点（屏幕中心）
            Vector3 cameraForward = (GetLookAtPoint() - tempPos).normalized;

            // 计算 yaw（水平角度） 
            _yaw = Mathf.Atan2(cameraForward.x, cameraForward.z) * Mathf.Rad2Deg;

            // 计算 pitch（俯仰角）
            float flatLen = new Vector2(cameraForward.x, cameraForward.z).magnitude;
            _pitch = Mathf.Atan2(cameraForward.y, flatLen) * Mathf.Rad2Deg;

            // 设置视野和灵敏度
            sensitivity = 0.02f;
            _virtualCamera.m_Lens.FieldOfView = 5f;

            // 初始同步
            UpdateFpsCamera();
        }

        /// <summary>
        /// 获取当前摄像机屏幕中心点所看的世界坐标点
        /// </summary>
        private Vector3 GetLookAtPoint()
        {
            var ray = new Ray(transform.position, transform.forward);
            if (Physics.Raycast(ray, out var hit, 300f))
            {
                Debug.DrawRay(ray.origin, ray.direction * 300f, Color.blue, 5f);
                return hit.point;
            }

            Debug.DrawRay(ray.origin, ray.direction * 300f, Color.green, 5f);

            // 如果没击中任何物体，就取一个前方的远点
            return ray.origin + ray.direction * 300f;
        }


        /// <summary>
        /// 调试函数：实时更新 FPS 摄像机位置与朝向
        /// </summary>
        private void UpdateFpsCamera()
        {
            if (targetPlayer == null) return;

            // 计算摄像机旋转
            Quaternion fpsRotation = Quaternion.Euler(-_pitch, _yaw, 0f);
            transform.rotation = fpsRotation;

            // 计算摄像机位置：角色位置 + 上偏移 + 前向偏移
            Vector3 upOffsetVec = Vector3.up * fpsUpOffset;
            Vector3 forwardOffsetVec = fpsRotation * Vector3.forward * fpsForwardOffset;

            transform.position = targetPlayer.position + upOffsetVec + forwardOffsetVec;

            // 使角色与摄像机方向保持一致（只保留 Y 轴旋转）
            Vector3 cameraForward = transform.forward;
            cameraForward.y = 0f; // 忽略俯仰
            if (cameraForward.sqrMagnitude > 0.001f)
            {
                targetPlayer.forward = cameraForward.normalized;
            }
        }

        #endregion
    }

    public enum BattleCameraMode
    {
        Commander,
        Tps,
        Fps,
    }
}