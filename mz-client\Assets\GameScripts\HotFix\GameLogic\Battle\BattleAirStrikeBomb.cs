using System.Collections;
using System.Collections.Generic;
using ChobiAssets.PTM;
using GameCommon;
using TEngine;
using UnityEngine;

namespace GameLogic
{
    public class BattleAirStrikeBomb : MonoBehaviour
    {
        public float explosionForce = 1000f;
        public float explosionRadius = 5f;
        Vector3 Destroyed_Effect_Offset;

        void Start()
        {
            Destroyed_Effect_Offset = Vector3.zero;
        }


        void OnCollisionEnter(Collision collision)
        {
            if (collision.gameObject.layer == BattleColliderLayer.Character
            || collision.gameObject.layer == BattleColliderLayer.Body
            || collision.gameObject.layer == BattleColliderLayer.Terrain)
            {
                Explode();
            }
        }

        void Explode()
        {
            Collider[] colliders = Physics.OverlapSphere(transform.position, explosionRadius);
            foreach (Collider hit in colliders)
            {
                Rigidbody rb = hit.GetComponent<Rigidbody>();
                if (rb != null)
                {
                    rb.AddExplosionForce(explosionForce, transform.position, explosionRadius);
                }

                var dcc = hit.GetComponent<Damage_Control_Center_CS>();
                if (dcc != null)
                    hit.GetComponent<Damage_Control_Center_CS>().Receive_Damage(100, 0, 0);
            }

            // Set the tag.
            this.transform.tag = "Finish";

            // Play explosion effect
            var Destroyed_Effect = GameModule.Resource.LoadGameObject("Destroyed_01 (Body)");

            Vector3 pos = this.transform.position + (this.transform.right * Destroyed_Effect_Offset.x) + (this.transform.up * Destroyed_Effect_Offset.y) +
                          (this.transform.forward * Destroyed_Effect_Offset.z);

            Destroyed_Effect.transform.position = pos;
            Destroyed_Effect.transform.rotation = this.transform.rotation;
            // Destroyed_Effect.transform.SetParent(this.transform);

            // Destroy bomb after explosion
            Destroy(gameObject, 0.5f);
        }
    }
}
