﻿using TEngine;
using UnityEngine;

namespace GameLogic
{
    public class BattleCameraManager
    {
        private CommanderCamera _commanderCamera;

        public BattleCameraManager(Transform cameraContent)
        {
            _commanderCamera = cameraContent.GetComponentInChildren<CommanderCamera>();
        }

        public void Initialize()
        {
            GameEvent.AddEventListener<Transform>(GameCommon.ActorEventDefine.TestChangeCameraLookAt, OnTestChangeCameraLookAt);
        }

        private void OnTestChangeCameraLookAt(Transform target)
        {
            _commanderCamera.ChangeCameraLookAt(target);
        }

        public void Release()
        {
            _commanderCamera = null;
            GameEvent.RemoveEventListener<Transform>(GameCommon.ActorEventDefine.TestChangeCameraLookAt, OnTestChangeCameraLookAt);
        }

        public CommanderCamera GetCmdCamera()
        {
            return _commanderCamera;
        }
    }
}