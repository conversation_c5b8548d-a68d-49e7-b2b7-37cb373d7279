%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-5636055368072792903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: RenderObjects
  m_EditorClassIdentifier: 
  m_Active: 0
  settings:
    passTag: RenderObjects
    Event: 300
    filterSettings:
      RenderQueueType: 0
      LayerMask:
        serializedVersion: 2
        m_Bits: 512
      PassNames: []
    overrideMaterial: {fileID: 2100000, guid: 60717c9e08a1c5443badd104cea4ebc6, type: 2}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 5
    enableWrite: 0
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: ForwardRenderer
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7,
      type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
  m_RendererFeatures:
  - {fileID: -5636055368072792903}
  - {fileID: 7878415144064880269}
  - {fileID: 3465578479645477718}
  - {fileID: 8293501254095260222}
  - {fileID: 5297387168151881720}
  m_RendererFeatureMap: b97496fb80b5c8b18d8e0bf7d4c0556dd0e67677926af01b3eca9a22736f1873f8c7670b90198449
  m_UseNativeRenderPass: 0
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  xrSystemData: {fileID: 11400000, guid: 60e1133243b97e347b653163a8c01b64, type: 2}
  shaders:
    blitPS: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
    copyDepthPS: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
    screenSpaceShadowPS: {fileID: 4800000, guid: 0f854b35a0cf61a429bd5dcfea30eddd,
      type: 3}
    samplingPS: {fileID: 4800000, guid: 04c410c9937594faa893a11dceb85f7e, type: 3}
    stencilDeferredPS: {fileID: 4800000, guid: e9155b26e1bc55942a41e518703fe304, type: 3}
    fallbackErrorPS: {fileID: 4800000, guid: e6e9a19c3678ded42a3bc431ebef7dbd, type: 3}
    fallbackLoadingPS: {fileID: 4800000, guid: 7f888aff2ac86494babad1c2c5daeee2, type: 3}
    materialErrorPS: {fileID: 4800000, guid: 5fd9a8feb75a4b5894c241777f519d4e, type: 3}
    coreBlitPS: {fileID: 4800000, guid: 93446b5c5339d4f00b85c159e1159b7c, type: 3}
    coreBlitColorAndDepthPS: {fileID: 4800000, guid: d104b2fc1ca6445babb8e90b0758136b,
      type: 3}
    blitHDROverlay: {fileID: 4800000, guid: a89bee29cffa951418fc1e2da94d1959, type: 3}
    cameraMotionVector: {fileID: 4800000, guid: c56b7e0d4c7cb484e959caeeedae9bbf,
      type: 3}
    objectMotionVector: {fileID: 4800000, guid: 7b3ede40266cd49a395def176e1bc486,
      type: 3}
    dataDrivenLensFlare: {fileID: 4800000, guid: 6cda457ac28612740adb23da5d39ea92,
      type: 3}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 0
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 0
  m_DepthPrimingMode: 0
  m_CopyDepthMode: 0
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 1
--- !u!114 &3465578479645477718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d111dc2a0b22b142804f4d59508b893, type: 3}
  m_Name: EPO Feature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &5297387168151881720
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d939433933ef4370b6210333b4ef1e34, type: 3}
  m_Name: NightVisionRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    nightVisionMaterial: {fileID: 2100000, guid: 77c5c11d4573bea4a80fe2ab181d4c61,
      type: 2}
    isNightVisionOn: 0
--- !u!114 &7878415144064880269
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: RenderObjects
  m_EditorClassIdentifier: 
  m_Active: 0
  settings:
    passTag: RenderObjects
    Event: 300
    filterSettings:
      RenderQueueType: 0
      LayerMask:
        serializedVersion: 2
        m_Bits: 2560
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 0
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &8293501254095260222
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a7ddadf4cf5b4c3990ea04d752f7cafc, type: 3}
  m_Name: ThermalVisionRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  thermalShader: {fileID: 4800000, guid: 8d0b7c3779921441bb3227619fa05094, type: 3}
  thermalHeatSourceShader: {fileID: 4800000, guid: ad93d12460f244d384f5a3d76b9cb735,
    type: 3}
  isThermalOn: 0
  paletteTexture: {fileID: 2800000, guid: dd15cd8ec183743268aff79c2548191a, type: 3}
  useTexture: 0
  coolColor: {r: 0, g: 0, b: 0, a: 0}
  midColor: {r: 0.18867922, g: 0.18867922, b: 0.18867922, a: 0}
  warmColor: {r: 1, g: 1, b: 1, a: 0}
  environmentTemperature: 0.2
