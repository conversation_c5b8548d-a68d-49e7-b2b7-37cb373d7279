﻿using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace GameLogic
{
    // 热成像-夜视模式开关脚本
    public class SpecialVisionSwitch : MonoBehaviour
    {
        public ThermalVisionRenderFeature thermalFeature; // 拖入 inspector
        public NightVisionRenderFeature nightFeature;

        public void NightVisionState(bool isOn)
        {
            if (nightFeature == null) return;

            nightFeature.settings.isNightVisionOn = isOn;
        }

        public void ThermalVisionState(bool isOn)
        {
            if (thermalFeature == null) return;

            thermalFeature.isThermalOn = isOn;

            ChangeVisioFlag(isOn);
        }

        private static void ChangeVisioFlag(bool isOn)
        {
            var roleList = FindObjectsOfType<RoleVisionSupport>();
            var treeList = FindObjectsOfType<TreeVisionSupport>();
            var tankList = FindObjectsOfType<TankVisionSupport>();

            foreach (var vision in roleList)
            {
                vision.SetRimLight(isOn);
            }

            foreach (var vision in treeList)
            {
                vision.SetTreeVision(isOn);
            }

            foreach (var vision in tankList)
            {
                vision.SetSpecialVision(isOn);
            }
        }
    }
}