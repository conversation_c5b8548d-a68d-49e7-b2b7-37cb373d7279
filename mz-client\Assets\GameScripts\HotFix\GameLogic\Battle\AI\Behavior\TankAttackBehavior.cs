﻿using UnityEngine;

namespace GameLogic
{
    public class TankAttackBehavior : IAttackBehavior
    {
        private readonly TankController _controller;

        public TankAttackBehavior(TankController controller)
        {
            _controller = controller;
        }

        public void OnAttackStart(Transform target)
        {
            _controller.AIScript.aimingScript.AI_Lock_On(target);
            _controller.AIScript.OpenFire_Flag = true;
        }

        public void OnAttackSustain()
        {
        }

        public void OnAttackEnd()
        {
            _controller.AIScript.aimingScript.AI_Lock_Off();
            _controller.AIScript.OpenFire_Flag = false;
        }

        public void EjectionAttack(Vector3 targetPos, uint uuid)
        {
        }

        public void AttackInDirection(Transform camTrans)
        {
            ;
        }
    }
}