%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MI_Grass_04
  m_Shader: {fileID: -6465566751694194690, guid: c82fb8953cb39e84b95e4362eeea2670,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: _Wind_Intensity
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GrassColorMap:
        m_Texture: {fileID: 2800000, guid: 2a6bfadef663e25499a0827260b7a435, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Grass_Color_Map:
        m_Texture: {fileID: 2800000, guid: 2a6bfadef663e25499a0827260b7a435, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _WindNoiseGray16:
        m_Texture: {fileID: 2800000, guid: 2a6bfadef663e25499a0827260b7a435, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Wind_Noise_Gray16:
        m_Texture: {fileID: 2800000, guid: 2a6bfadef663e25499a0827260b7a435, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Emiision: 0
    - _EnvironmentReflections: 1
    - _GlobalColorMaskSize: 50
    - _Global_Color_Mask_Size: 30
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GroundColorGradient: 9.52
    - _Ground_Color_Gradient: 6.59
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _PerBladeVariation: 0.2
    - _Per_Blade_Variation: 0.2
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _SSSFresnelGradient: 6
    - _SSSFresnelIntensity: 0.19
    - _SSS_Fresnel_Gradient: 8.22
    - _SSS_Fresnel_Intensity: 0.19
    - _SmoothnessTextureChannel: 0
    - _Smothness: 0
    - _Specular: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SubsurfacePower: 5.15
    - _SubsurfaceVerticalGradient: 0.51
    - _Subsurface_Power: 2.1
    - _Subsurface_Vertical_Gradient: 0.79
    - _UVSec: 0
    - _WindAsinc: 0.1
    - _WindIntensity: 1.5
    - _WindTimeScale: 1.2
    - _WindWaveScale: 50
    - _Wind_Asinc: 0.2
    - _Wind_Intensity: 0.05
    - _Wind_Time_Scale: 1
    - _Wind_Wave_Scale: 50
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _BottomColor: {r: 0.5188679, g: 0.3581775, b: 0.13053277, a: 1}
    - _Bottom_Color: {r: 0.21844698, g: 0.4433962, b: 0.123398006, a: 1}
    - _Color: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GroundColor: {r: 0.53773576, g: 0.42718333, b: 0.21137404, a: 0}
    - _Ground_Color: {r: 0.15003303, g: 0.69803923, b: 0.12941174, a: 1}
    - _SubsurfaceTint: {r: 1, g: 0.78651786, b: 0.2421382, a: 0}
    - _Subsurface_Tint: {r: 0.24313724, g: 1, b: 0.38679144, a: 1}
    - _TopColor: {r: 0.8396226, g: 0.6833646, b: 0.33004025, a: 0}
    - _Top_Color: {r: 0.4577741, g: 1, b: 0.062745094, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &5683918317166564372
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
