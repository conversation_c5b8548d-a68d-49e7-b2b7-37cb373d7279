﻿using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace GameLogic
{
    public class ThermalVisionRenderFeature : ScriptableRendererFeature
    {
        private static readonly int EnvTempID = Shader.PropertyToID("_EnvironmentTemperature");
        private static readonly int UseTextureID = Shader.PropertyToID("useTexture");
        private static readonly int PaletteTexID = Shader.PropertyToID("_PaletteTex");
        private static readonly int CoolColorID = Shader.PropertyToID("coolColor");
        private static readonly int MidColorID = Shader.PropertyToID("midColor");
        private static readonly int WarmColorID = Shader.PropertyToID("warmColor");
        private static readonly int HeatMapTexID = Shader.PropertyToID("_HeatMapTex");

        class ThermalVisionPass : ScriptableRenderPass
        {
            private readonly Material _thermalMat;
            private readonly Material _thermalHeatSourceMat;
            private RenderTargetHandle _tempTexture;
            private RenderTargetHandle _thermalHeatMap;
            private readonly string _profilerTag;

            public bool IsThermalOn;
            public Texture PaletteTexture;
            public int UseTexture;
            public Color CoolColor;
            public Color MidColor;
            public Color WarmColor;
            public float EnvironmentTemperature;

            public ThermalVisionPass(string profilerTag, Shader thermalShader, Shader heatSourceShader)
            {
                this._profilerTag = profilerTag;

                if (thermalShader != null)
                    _thermalMat = new Material(thermalShader);

                if (heatSourceShader != null)
                    _thermalHeatSourceMat = new Material(heatSourceShader);

                _tempTexture.Init("_TempThermalVisionTex");
                _thermalHeatMap.Init("_ThermalHeatMap");
            }

            private bool _isChanged;

            public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
            {
                if (_thermalMat == null || _thermalHeatSourceMat == null)
                    return;
                if (!IsThermalOn) return;

                var cmd = CommandBufferPool.Get(_profilerTag);

                _thermalMat.SetFloat(EnvTempID, EnvironmentTemperature);

                if (UseTexture == 1 || UseTexture == 2)
                {
                    _thermalMat.SetInt(UseTextureID, UseTexture);
                    _thermalMat.SetTexture(PaletteTexID, PaletteTexture);
                }
                else
                {
                    _thermalMat.SetInt(UseTextureID, 0);
                    _thermalMat.SetColor(CoolColorID, CoolColor);
                    _thermalMat.SetColor(MidColorID, MidColor);
                    _thermalMat.SetColor(WarmColorID, WarmColor);
                }

                RenderTextureDescriptor desc = renderingData.cameraData.cameraTargetDescriptor;
                desc.depthBufferBits = 24;

                RTHandle targetHandle = renderingData.cameraData.renderer.cameraColorTargetHandle;
                RenderTargetIdentifier source = targetHandle;

                cmd.GetTemporaryRT(_tempTexture.id, desc, FilterMode.Bilinear);
                cmd.GetTemporaryRT(_thermalHeatMap.id, desc, FilterMode.Bilinear);
                cmd.SetRenderTarget(
                    _thermalHeatMap.Identifier(),
                    renderingData.cameraData.renderer.cameraDepthTargetHandle
                );
                cmd.ClearRenderTarget(false, true, Color.black); // 清除深度 + 颜色

                // ✅ Step 2: 后处理合成热成像
                cmd.SetGlobalTexture(HeatMapTexID, _thermalHeatMap.Identifier());
                cmd.Blit(source, _tempTexture.Identifier());
                cmd.Blit(_tempTexture.Identifier(), source, _thermalMat);

                context.ExecuteCommandBuffer(cmd);
                CommandBufferPool.Release(cmd);
            }
            

            public override void FrameCleanup(CommandBuffer cmd)
            {
                if (cmd == null) return;
                cmd.ReleaseTemporaryRT(_tempTexture.id);
                cmd.ReleaseTemporaryRT(_thermalHeatMap.id);
            }
        }

        public Shader thermalShader;
        public Shader thermalHeatSourceShader;

        private ThermalVisionPass _thermalVisionPass;

        public bool isThermalOn = true;
        public Texture paletteTexture;
        public int useTexture;
        public Color coolColor = Color.black;
        public Color midColor = Color.gray;
        public Color warmColor = Color.white;
        public float environmentTemperature = 0.2f;

        public override void Create()
        {
            _thermalVisionPass = new ThermalVisionPass("ThermalVisionPass", thermalShader, thermalHeatSourceShader)
            {
                renderPassEvent = RenderPassEvent.AfterRenderingTransparents
            };
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            _thermalVisionPass.IsThermalOn = isThermalOn;
            _thermalVisionPass.PaletteTexture = paletteTexture;
            _thermalVisionPass.UseTexture = useTexture;
            _thermalVisionPass.CoolColor = coolColor;
            _thermalVisionPass.MidColor = midColor;
            _thermalVisionPass.WarmColor = warmColor;
            _thermalVisionPass.EnvironmentTemperature = environmentTemperature;

            renderer.EnqueuePass(_thermalVisionPass);
        }
    }
}