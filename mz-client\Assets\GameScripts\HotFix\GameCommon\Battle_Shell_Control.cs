using System.Collections;
using System.Collections.Generic;
using TEngine;
using UnityEngine;

namespace GameCommon
{
    /// <summary>
    /// 子弹控制脚本
    /// 调用思路与Bullet_Control_CS相同
    /// </summary>
    [AddComponentMenu("ZGame/Shell_Control")]
    public class Battle_Shell_Control : MonoBehaviour
    {
        [HideInInspector] public ShellAttribute Attr; //子弹属性
        [HideInInspector] public float Life_Time; //子弹生命周期
        [HideInInspector] public float Attack_Multiplier = 1.0f; // 攻击倍率
        [HideInInspector] public float Initial_Velocity; // 初始速度
        [HideInInspector] public bool isEjectionMode; // 是否是抛射模式
        [HideInInspector] public uint shellUuid;

        [SerializeField] GameObject Explosion_Object;
        [HideInInspector] public float Explosion_Force;
        [HideInInspector] public float Explosion_Radius;


        [SerializeField] public bool Debug_Flag; // 是否开启调试模式
        GameObject Owner;
        bool isLiving = true; // 子弹是否存活
        Transform This_Transform;
        Rigidbody This_Rigidbody;
        [HideInInspector] public bool isFlying = false;

        void Start()
        {
            Initialize();

            if (Attr == null)
            {
                Attr = new ShellAttribute();
                Attr.ShellType = ShellType.AP;
                Attr.PhysicalAttack = 10;
            }

            // Initial_Velocity = 200f;
            // This_Rigidbody.AddForce(This_Transform.forward * Initial_Velocity);
            // This_Rigidbody = GetComponent<Rigidbody>();
            // var currentVelocity = transform.forward * 20f;
            // This_Rigidbody.velocity = currentVelocity;

        }

        void Initialize()
        {
            if (This_Transform == null)
            {
                This_Transform = transform;
            }
            if (This_Rigidbody == null)
            {
                This_Rigidbody = GetComponent<Rigidbody>();
            }

            // Set the collision detection mode.
            This_Rigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;

            Destroy(this.gameObject, Life_Time);
        }


        private void Update()
        {
            if (isFlying)
                RotateShellWithGravity();
        }


        // 使炮弹受重力影响调整旋转，使其朝地面旋转
        private void RotateShellWithGravity()
        {
            // 获取当前速度的方向
            Vector3 velocityDirection = This_Rigidbody.velocity.normalized;

            // 获取地面方向，通常是重力的反方向
            Vector3 gravityDirection = -Physics.gravity.normalized;

            // 计算炮弹的目标旋转方向，使其朝向飞行的方向并考虑重力
            Quaternion targetRotation = Quaternion.LookRotation(velocityDirection, gravityDirection);

            // 平滑旋转，调整旋转速度使其自然过渡
            This_Rigidbody.rotation = Quaternion.RotateTowards(This_Rigidbody.rotation, targetRotation, 100f * Time.deltaTime);
        }

        void OnCollisionEnter(Collision collision)
        {
            // The collision has been detected by the physics engine.
            if (isLiving)
            {
                switch (this.Attr.ShellType)
                {
                    case ShellType.AP: // AP
                    case ShellType.APCR:
                    case ShellType.APDS:
                    case ShellType.APFSDS:
                        AP_Hit_Process(collision.collider.gameObject, collision.relativeVelocity.magnitude, collision.contacts[0].normal);
                        break;
                    case ShellType.HE: // HE
                    case ShellType.HEAT:
                    case ShellType.HESH:
                        HE_Hit_Process();
                        break;
                }
            }
        }

        void AP_Hit_Process(GameObject hitObject, float hitVelocity, Vector3 hitNormal)
        {
            Debug.Log("AP弹命中目标 ： " + hitObject.name);
            isLiving = false;
            if (isEjectionMode)
                GameEvent.Send(ActorEventDefine.BattleEjectionHit, shellUuid);

            // Set the collision detection mode.
            This_Rigidbody.collisionDetectionMode = CollisionDetectionMode.Discrete;

            if (hitObject == null)
            {
                // The hit object had been removed from the scene.
                return;
            }

            var damageScript = hitObject.GetComponent<Damage_Control_Base>();
            if (damageScript != null)
            {
                var hitAngle = Mathf.Abs(90.0f - Vector3.Angle(This_Transform.forward, hitNormal));
                var damageValue = this.Attr.PhysicalAttack * Mathf.Pow(hitVelocity / Initial_Velocity, 2.0f) * Mathf.Lerp(0.0f, 1.0f, Mathf.Sqrt(hitAngle / 90.0f)) * Attack_Multiplier;

                // Send the damage value to "Damage_Control_##_##_CS" script.
                if (damageScript.Get_Damage(damageValue, this.Attr.ShellType) == true)
                {
                    // The hit part has been destroyed.
                    // Remove the bullet from the scene.
                    if (Debug_Flag)
                    {
                        Debug.Log("AP Damage " + damageValue + " on " + hitObject.name);
                    }
                }
            }
            Destroy(this.gameObject);
            if (GameModule.Resource != null)
            {
                // Debug.Log("播放击中特效");
                GameObject ricochet_Object = GameModule.Resource.LoadGameObject("Ricochet_01");
                ricochet_Object.transform.position = This_Transform.position;
                ricochet_Object.transform.rotation = Quaternion.identity;
                ricochet_Object.transform.SetParent(hitObject.transform);
            }
        }

        void HE_Hit_Process()
        {
            isLiving = false;

            // Create the explosion effect object.
            if (Explosion_Object)
            {
                Instantiate(Explosion_Object, This_Transform.position, Quaternion.identity);
            }

            // Remove the useless components.
            Destroy(GetComponent<Renderer>());
            Destroy(GetComponent<Rigidbody>());
            Destroy(GetComponent<Collider>());

            // Add the explosion force to the objects within the explosion radius.
            var colliders = Physics.OverlapSphere(This_Transform.position, Explosion_Radius, BattleColliderLayer.Layer_Mask);
            for (int i = 0; i < colliders.Length; i++)
            {
                Add_Explosion_Force(colliders[i]);
            }

            Destroy(this.gameObject, 0.01f * Explosion_Radius);
        }
        void Add_Explosion_Force(Collider collider)
        {
            if (collider == null)
            {
                return;
            }

            Vector3 direction = (collider.transform.position - This_Transform.position).normalized;
            var ray = new Ray();
            ray.origin = This_Rigidbody.position;
            ray.direction = direction;
            if (Physics.Raycast(ray, out RaycastHit raycastHit, Explosion_Radius, BattleColliderLayer.Layer_Mask))
            {
                if (raycastHit.collider != collider)
                { // The collider should be behind an obstacle.
                    return;
                }

                // Calculate the distance loss rate.
                var loss = Mathf.Pow((Explosion_Radius - raycastHit.distance) / Explosion_Radius, 2);

                // Add force to the rigidbody.
                Rigidbody rigidbody = collider.GetComponent<Rigidbody>();
                if (rigidbody)
                {
                    rigidbody.AddForce(direction * Explosion_Force * loss);
                }

                // Send the damage value to "Damage_Control_##_##_CS" script in the collider.
                var damageScript = collider.GetComponent<Damage_Control_Base>();
                if (damageScript != null)
                { // The collider should be a breakable object.
                    var damageValue = this.Attr.ChemicalAttack * loss * Attack_Multiplier;
                    damageScript.Get_Damage(damageValue, ShellType.HE);
                    // Output for debugging.
                    if (Debug_Flag)
                    {
                        Debug.Log("HE Damage " + damageValue + " on " + collider.name);
                    }
                }
            }
        }

        /// <summary>
        /// Set the character or gameobject that fired the bullet, some components compare the owners in some events to know whether or not to perform an action, for example the Hit Marker Component.
        /// </summary>
        /// <param name="owner">bullet owner</param>
        public void SetOwner(GameObject owner)
        {
            Owner = owner;
        }
    }
}