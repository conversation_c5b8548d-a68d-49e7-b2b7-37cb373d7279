using TEngine;

namespace GameCommon
{
    public static class ActorEventDefine
    {
        // 通用不带前缀
        public static readonly int Loading = RuntimeId.ToRuntimeId("ActorEventDefine.Loading");
        public static readonly int CameraState = RuntimeId.ToRuntimeId("ActorEventDefine.CameraState");
        public static readonly int FocusPropValueChange = RuntimeId.ToRuntimeId("ActorEventDefine.FocusPropValueChange"); // 焦点道具属性值改变
        public static readonly int ActivityCenterShowEvent = RuntimeId.ToRuntimeId("ActorEventDefine.ActivityCenterShowEvent"); // 活动中心显示事件

        #region 用于类似编队的通用弹窗

        public static readonly int RoleSelect = RuntimeId.ToRuntimeId("ActorEventDefine.RoleSelect"); // 角色选择
        public static readonly int RoleSelectEnd = RuntimeId.ToRuntimeId("ActorEventDefine.RoleSelectEnd"); // 角色选择结束

        #endregion

        // 专属需要带UI名称前缀
        public static readonly int ChapterLevelDialog = RuntimeId.ToRuntimeId("UIChapterEventDefine.ChapterLevelDialog");
        public static readonly int ChapterLTheatreDialog = RuntimeId.ToRuntimeId("UIChapterEventDefine.ChapterLTheatreDialog");
        public static readonly int UIFormationSelectTeam = RuntimeId.ToRuntimeId("UIFormation.UIFormationSelectTeam");

        // 更新货币数量
        public static readonly int UpdateCurrency = RuntimeId.ToRuntimeId("ActorEventDefine.UpdateCurrency");

        // 摄像机视角锁定
        public static readonly int BattleCameraLock = RuntimeId.ToRuntimeId("ActorEventDefine.BattleCameraLock"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int BattleEjectionHit = RuntimeId.ToRuntimeId("ActorEventDefine.BattleEjectionHit"); // 抛射命中计数

        // 战斗流程事件
        public static readonly int BattleRolePrefabLoaded = RuntimeId.ToRuntimeId("ActorEventDefine.BattleRolePrefabLoaded"); // 角色加载完毕
        public static readonly int BattleDamageTaken = RuntimeId.ToRuntimeId("ActorEventDefine.BattleDamageTaken"); // 受到伤害
        public static readonly int BattleEnd = RuntimeId.ToRuntimeId("ActorEventDefine.BattleEnd"); // 战斗结束
        public static readonly int PlayerAction = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerAction"); // 角色行动
        public static readonly int BattleLeaderDead = RuntimeId.ToRuntimeId("ActorEventDefine.BattleLeaderDead"); // 领队死亡
        public static readonly int BattleSelectUnit = RuntimeId.ToRuntimeId("ActorEventDefine.BattleSelectUnit"); // 选中角色
        public static readonly int BattleMoveDeployed = RuntimeId.ToRuntimeId("ActorEventDefine.BattleMoveDeployed"); //  角色部署--确认移动目标
        public static readonly int BattleSelectProp = RuntimeId.ToRuntimeId("ActorEventDefine.BattleSelectProp"); //  选择道具
        public static readonly int BattleUnitWakeUp = RuntimeId.ToRuntimeId("ActorEventDefine.BattleUnitWakeUp"); //  单位警觉
        public static readonly int BattleCmdExecutionCompleted = RuntimeId.ToRuntimeId("ActorEventDefine.BattleCmdExecutionCompleted"); //  单位警觉
        public static readonly int BattleArtillery = RuntimeId.ToRuntimeId("ActorEventDefine.BattleArtillery"); //  炮击
        public static readonly int BattleCmdContentRefresh = RuntimeId.ToRuntimeId("ActorEventDefine.BattleCmdExecutionCompleted"); //  单位警觉
        public static readonly int ReactionSpeedEvent = RuntimeId.ToRuntimeId("ActorEventDefine.ReactionSpeedEvent"); //  换弹事件
        public static readonly int BattleSetCameraMode = RuntimeId.ToRuntimeId("ActorEventDefine.BattleSetCameraMode"); //  换弹事件

        // 玩家界面事件
        public static readonly int PlayerInfoChangeSecGroup = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerInfoChangeSecGroup"); // 更改秘书组
        public static readonly int PlayerInfoChangePlayerImg = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerInfoChangePlayerImg"); // 更改玩家立绘
        public static readonly int PlayerInfoSelectPlayerImg = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerInfoSelectPlayerImg"); // 选择玩家立绘
        public static readonly int PlayerInfoChangeNickName = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerInfoChangeNickName"); // 更改玩家昵称
        public static readonly int PlayerInfoSelectHallImg = RuntimeId.ToRuntimeId("ActorEventDefine.PlayerInfoSelectHallImg"); // 选择大厅背景

        // 商城界面事件
        public static readonly int ShopBuyCommodity = RuntimeId.ToRuntimeId("ActorEventDefine.ShopBuyCommodity"); // 购买商品
        public static readonly int ShopShowNormalMaterial = RuntimeId.ToRuntimeId("ActorEventDefine.ShopShowNormalMaterial"); // 展示材料信息
        public static readonly int ShopShowTradeMaterial = RuntimeId.ToRuntimeId("ActorEventDefine.ShopShowTradeMaterial"); // 展示材料信息
        public static readonly int ShopBuyMaterial = RuntimeId.ToRuntimeId("ActorEventDefine.ShopBuyMaterial"); // 购买材料
        public static readonly int ShopSwitchMarket = RuntimeId.ToRuntimeId("ActorEventDefine.ShopSwitchMarket"); // 切换交易市场
        public static readonly int ShopSelectSkin = RuntimeId.ToRuntimeId("ActorEventDefine.ShopSelectSkin"); // 选中皮肤
        public static readonly int ShopSwitchSkinSeries = RuntimeId.ToRuntimeId("ActorEventDefine.ShopSwitchSkinSeries"); // 切换皮肤系列
        public static readonly int ShopBuySkin = RuntimeId.ToRuntimeId("ActorEventDefine.ShopBuySkin"); // 选中皮肤
        public static readonly int ShopRechargeGold = RuntimeId.ToRuntimeId("ActorEventDefine.ShopRechargeGold"); // 充值黄金储备
        public static readonly int ShopRefreshPage = RuntimeId.ToRuntimeId("ActorEventDefine.ShopRefreshPage"); // 刷新界面


        // 实验室界面事件
        public static readonly int LabSelectProject = RuntimeId.ToRuntimeId("ActorEventDefine.LabSelectProject"); // 选中实验项目
        public static readonly int LabUnLockProject = RuntimeId.ToRuntimeId("ActorEventDefine.LabUnLockProject"); // 解锁实验项目
        public static readonly int LabFinishProject = RuntimeId.ToRuntimeId("ActorEventDefine.LabFinishProject"); // 完成实验项目


        // 游戏指南界面事件
        public static readonly int GameGuideSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.GameGuideSelectOne"); // 选中指南

        // 养成界面事件
        public static readonly int CulReformSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.CulReformSelectOne"); // 选中一个养成项目
        public static readonly int CulReformUnLockOne = RuntimeId.ToRuntimeId("ActorEventDefine.CulReformUnLockOne"); //解锁一个养成项目
        public static readonly int CulSKinInfoSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.CulSKinInfoSelectOne"); // 选中一个皮肤
        public static readonly int CulSKinInfoChangeSkin = RuntimeId.ToRuntimeId("ActorEventDefine.CulSKinInfoChangeSkin"); // 选中一个皮肤
        public static readonly int CulShowWeaponDialog = RuntimeId.ToRuntimeId("ActorEventDefine.CulShowWeaponDialog"); // 选中一个皮肤
        public static readonly int CulShowBulletDialog = RuntimeId.ToRuntimeId("ActorEventDefine.CulShowBulletDialog"); // 选中一个皮肤
        public static readonly int CulShowEquipmentDialog = RuntimeId.ToRuntimeId("ActorEventDefine.CulShowEquipmentDialog"); // 选中一个皮肤
        public static readonly int CulShowSkillDialog = RuntimeId.ToRuntimeId("ActorEventDefine.CulShowSkillDialog"); // 选中一个皮肤
        public static readonly int CulCloseDialog = RuntimeId.ToRuntimeId("ActorEventDefine.CulCloseDialog"); // 选中一个皮肤

        // 派遣界面事件--建设任务
        public static readonly int DispatchSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.DispatchSelectOne"); //选中一个派遣项目
        public static readonly int DispatchClaimRewards = RuntimeId.ToRuntimeId("ActorEventDefine.DispatchClaimRewards"); // 领取派遣任务奖励
        public static readonly int DispatchStartOne = RuntimeId.ToRuntimeId("ActorEventDefine.DispatchStartOne"); //开始一个派遣项目


        // 每日、主线任务界面
        public static readonly int TaskClaimRewards = RuntimeId.ToRuntimeId("ActorEventDefine.TaskClaimRewards"); // 领取任务奖励
        public static readonly int TaskSelectMainTask = RuntimeId.ToRuntimeId("ActorEventDefine.TaskSelectMainTask"); // 领取任务奖励

        // 仓库界面事件
        public static readonly int UIWareHouseSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.UIWareHouseSelectOne"); // 选择仓库中的道具

        // 邮件选中事件
        public static readonly int MailSelectOne = RuntimeId.ToRuntimeId("ActorEventDefine.MailSelectOne"); // 选中邮件

        // 大厅界面
        public static readonly int UIHallClickAvater = RuntimeId.ToRuntimeId("ActorEventDefine.UIHallClickAvater"); // 点击角色 -- 显示对话框
        public static readonly int UIHallChangeBg = RuntimeId.ToRuntimeId("ActorEventDefine.UIHallChangeBg"); // 更换背景

        // 通行证事件
        public static readonly int UISeasonCardReceProp = RuntimeId.ToRuntimeId("ActorEventDefine.UISeasonCardReceProp"); // 领取道具

        // 角色选择页面
        public static readonly int UIRoleSelectChoiceOne = RuntimeId.ToRuntimeId("ActorEventDefine.UIRoleSelectChoiceOne"); // 选择一个角色
        public static readonly int UIRoleSelectResult = RuntimeId.ToRuntimeId("ActorEventDefine.UIRoleSelectResult"); // 选择一个角色

        // 聊天页面
        public static readonly int UIChatSelectContact = RuntimeId.ToRuntimeId("ActorEventDefine.UIChatSelectContact"); // 选择一个联系人
        public static readonly int UIChatSelectMomentItem = RuntimeId.ToRuntimeId("ActorEventDefine.UIChatSelectMomentItem"); // 选择朋友圈消息
        public static readonly int UIChatUpdateFinalMsg = RuntimeId.ToRuntimeId("ActorEventDefine.UIChatUpdateFinalMsg"); // 更新最后一条消息


        #region 战斗测试

        // 通用不带前缀 -- 战斗测试
        public static readonly int UIBattleCheckCurrRole = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleCheckCurrRole"); // 选择当前角色
        public static readonly int UIBattleSendingInstruction = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleSendingInstruction"); // 发送指令
        public static readonly int UIBattleRoleDead = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleRoleDead"); // 角色死亡
        public static readonly int UIBattleRoleDamage = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleRoleDamage"); // 角色受伤

        public static readonly int UIBattleSelectMovePoint = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleSelectMovePoint"); // 角色选择移动点
        public static readonly int UIBattleSelectHateTarget = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleSelectHateTarget"); // 角色选择移动点
        public static readonly int UIBattleSelectChargeTarget = RuntimeId.ToRuntimeId("ActorEventDefine.UIBattleSelectChargeTarget"); // 角色选择移动点

        // 测试用
        public static readonly int TestChangeAIEnableState = RuntimeId.ToRuntimeId("ActorEventDefine.TestChangeAIEnableState");
        public static readonly int TestUnitBeFound = RuntimeId.ToRuntimeId("ActorEventDefine.TestUnitBeFound");
        public static readonly int TestChangeDoubleClickState = RuntimeId.ToRuntimeId("ActorEventDefine.TestChangeDoubleClickState"); // 发起指令
        public static readonly int TestChangeCameraLookAt = RuntimeId.ToRuntimeId("ActorEventDefine.TestChangeCameraLookAt"); // 切换摄像机对齐角色
        public static readonly int TestShowErrorTip = RuntimeId.ToRuntimeId("ActorEventDefine.TestShowErrorTip"); // 错误弹窗

        public static readonly int RegisterHealthBarUI = RuntimeId.ToRuntimeId("ActorEventDefine.RegisterHealthBarUI"); // 注册血条UI
        public static readonly int TestStartGame = RuntimeId.ToRuntimeId("ActorEventDefine.TestStartGame"); // 开始游戏
        public static readonly int TestConfirmMoveTarget = RuntimeId.ToRuntimeId("ActorEventDefine.TestConfirmMoveTarget"); // 确认移动目标
        public static readonly int TestPrepSelectMoveTarget = RuntimeId.ToRuntimeId("ActorEventDefine.TestPrepSelectMoveTarget"); // 准备选择移动目标
        public static readonly int TestLeaderMoveStart = RuntimeId.ToRuntimeId("ActorEventDefine.TestLeaderMoveStart"); // 领队开始移动
        public static readonly int TestLeaderMoveStop = RuntimeId.ToRuntimeId("ActorEventDefine.TestLeaderStopStart"); // 领队停止移动
        public static readonly int TestSetLeader = RuntimeId.ToRuntimeId("ActorEventDefine.TestSetLeader"); // 设置领队
        public static readonly int TestInitInputAction = RuntimeId.ToRuntimeId("ActorEventDefine.TestInitInputAction"); // 初始化InputAction
        public static readonly int TestSetLeaderMoveState = RuntimeId.ToRuntimeId("ActorEventDefine.TestSetLeaderMoveState"); // 设置领队移动状态 -- 启用或关闭移动

        public static readonly int TestTacticalSupportState = RuntimeId.ToRuntimeId("ActorEventDefine.TestTacticalSupportState"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int TestPreviewAoeSkill = RuntimeId.ToRuntimeId("ActorEventDefine.TestPreviewAoeSkill"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int TestReleaseAoeSkill = RuntimeId.ToRuntimeId("ActorEventDefine.TestReleaseAoeSkill"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int TestCancelAoeSkill = RuntimeId.ToRuntimeId("ActorEventDefine.TestCancelAoeSkill"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int TestSelectAttackTarget = RuntimeId.ToRuntimeId("ActorEventDefine.TestSelectAttackTarget"); // 设置领队移动状态 -- 启用或关闭移动
        public static readonly int TestRestoreUI = RuntimeId.ToRuntimeId("ActorEventDefine.TestRestoreUI"); // 设置领队移动状态 -- 启用或关闭移动

        #endregion
    }
}