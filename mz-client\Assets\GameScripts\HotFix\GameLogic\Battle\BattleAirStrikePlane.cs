using GameCommon;
using TableModule;
using TEngine;
using UnityEngine;

namespace GameLogic
{
    public class BattleAirStrikePlane : MonoBehaviour
    {
        [SerializeField] Transform DropBombPos;
        [SerializeField] GameObject BombPrefab;

        float flyOverHeight = 100f; // 飞机飞行的最大高度
          float rotateSpeed = 2f; // 飞机转向速度

        Vector3 initialVelocity; // 炸弹初始速度

        Transform targetPos;
        Vector3 targetFlyoverPoint;

        int DropCount = 1;

        // Start is called before the first frame update
        void Start()
        {
            var unitActor = BattleSystem.Instance.GetLeader();
            if (unitActor != null)
                targetPos = unitActor.transform;

            GameModule.UI.ShowUIAsync<UIAlarm>("空袭警告");
        }

        // Update is called once per frame
        void Update()
        {
            transform.Translate(Vector3.forward * BattleDefined.AirStrikePlaneSpeed * Time.deltaTime);

            if (targetPos == null)
                return;

            if (!HasPassedTarget())
            {
                // 1. 目标上空点
                targetFlyoverPoint = new Vector3(
                    targetPos.position.x,
                    targetPos.position.y + flyOverHeight,
                    targetPos.position.z
                );

                // 2. 方向向量（飞机朝向目标点）
                Vector3 direction = (targetFlyoverPoint - transform.position).normalized;

                // 3. 平滑旋转朝向目标点
                Quaternion targetRotation = Quaternion.LookRotation(direction);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * rotateSpeed);
            }

            if (ShouldDropBomb(80f, 120f))
            {
                DropBomb();
            }
        }

        // 判断是否到达目标上空点
        bool HasPassedTarget()
        {
            Vector3 toTarget = (targetFlyoverPoint - transform.position).normalized;
            return Vector3.Dot(transform.forward, toTarget) < 0;
        }

        // 判断是否应投弹（支持自定义提前投弹参数）
        bool ShouldDropBomb(float dropLeadDistanceMin = 0f, float dropLeadDistanceMax = 0f)
        {
            float flyOverHeight = DropBombPos.position.y;
            float gravity = Mathf.Abs(Physics.gravity.y);

            Vector3 dropPos = DropBombPos.position;
            Vector3 targetXZ = new Vector3(targetFlyoverPoint.x, 0f, targetFlyoverPoint.z);
            Vector3 dropXZ = new Vector3(dropPos.x, 0f, dropPos.z);
            Vector3 forwardDir = new Vector3(transform.forward.x, 0f, transform.forward.z).normalized;

            float fallTime = Mathf.Sqrt(2f * flyOverHeight / gravity);
            Vector3 predictedHitPoint = dropXZ + forwardDir * BattleDefined.AirStrikePlaneSpeed * fallTime;

            float currentDistanceToTarget = Vector3.Distance(dropXZ, targetXZ);
            float predictedDistanceToTarget = Vector3.Distance(predictedHitPoint, targetXZ);

            // 可控投弹提前量（负值则延后，正值提前）
            float section = currentDistanceToTarget - predictedDistanceToTarget;
            return section >= dropLeadDistanceMin && section <= dropLeadDistanceMax;
        }



        void DropBomb()
        {
            // Debug.Log("DropBomb");

            // var bomb = Instantiate(BombPrefab, DropBombPos.position, Quaternion.identity);

            var bomb = GameModule.Resource.LoadGameObject("HE_Bullet (AirStrike)");

            // 正负10f的随机值
            float randomOffsetX = Random.Range(-10f, 10f);
            float randomOffsetZ = Random.Range(-10f, 10f);
            bomb.transform.position = new Vector3(DropBombPos.position.x + randomOffsetX, DropBombPos.position.y, DropBombPos.position.z + randomOffsetZ);
            bomb.transform.LookAt(targetPos);

            Rigidbody rb = bomb.GetComponent<Rigidbody>();
            if (rb)
            {
                Vector3 forward = transform.forward;
                Vector3 slightDownward = (forward + Vector3.down * 0.2f).normalized;
                initialVelocity = slightDownward * BattleDefined.AirStrikePlaneSpeed;
                rb.velocity = initialVelocity;
            }
        }

    }
}
