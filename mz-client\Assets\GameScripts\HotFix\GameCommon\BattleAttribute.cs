using System.Collections.Generic;
using System.Linq;

namespace GameCommon
{
    // 部件类型
    public enum PartType
    {
        Body = 1, // 身体 角色与坦克公用
        Head,  // 头部
        Leg, // 腿部
        Arm, // 手臂
        Track, // 履带
        Turret, // 炮塔
    }

    public enum ShellType
    {
        AP = 1, // 穿甲弹
        APCR, // 高速穿甲弹
        APDS, // 脱壳穿甲弹
        APFSDS, // 尾翼稳定脱壳穿甲弹
        ATGM, // 反坦克导弹
        HE, // 高爆弹
        HEAT, // 破甲弹
        HESH, // 碎甲弹
        S, // 烟雾弹
    }

    // 部件属性
    public class PartAttribute
    {
        public int Hp { get; set; } // 生命值
        public int MaxHp { get; set; } // 生命值
        public int KineticArmor { get; set; } // 动能装甲厚度 单位/mm
        public int ChemicalArmor { get; set; } // 聚能装甲厚度 单位/mm
        public int OrdinaryRicochet { get; set; } // 普通炮弹 跳弹率
        public int AdvancedRicochet { get; set; } // 高级炮弹 跳弹率
        public int GrenadeRicochet { get; set; } // 榴弹 跳弹率
    }

    /// <summary>
    /// 弹药属性
    /// </summary>
    public class ShellAttribute
    {
        public ShellType ShellType { get; set; } // 弹药类型
        public int PhysicalAttack { get; set; } // 物理伤害
        public int ChemicalAttack { get; set; } // 化学伤害
        public int PiercingDepth { get; set; } // 穿透深度
        public int MaximumEntryAngle { get; set; } // 最大入射角
        public int CutAngleCorrection { get; set; } // 切角修正
        public int StartingSpeed { get; set; } // 初始速度
        public int Weight { get; set; } // 质量
    }

    /// 武器属性
    /// </summary>
    /// public class WeaponAttribute
    public class WeaponAttribute
    {
        public int WeaponType { get; set; }  // 武器类型
        public int Antiaircraft { get; set; } // 防空
        public float ReactionSpeed { get; set; } // 装填时间间隔 毫秒
        public int MagazineLength { get; set; } // 弹夹长度
        public int CurShellCount { get; set; } // 当前子弹数量
        public float ShortReaction { get; set; } // 连续射击间隔 毫秒
        public int LongPressShoot { get; set; } // 长按射击
        public int GunNumber { get; set; } // 枪数
        public int AmmunitionReserve { get; set; } // 弹药储备
        public Dictionary<int, ShellAttribute> Shells { get; set; } // 弹药属性
        public int CurShellId = 0; // 当前子弹id

        public ShellAttribute GetCurShellAttr()
        {
            return Shells[CurShellId];
        }
    }


    /// <summary>
    /// 角色属性 原始值-概率均为万分比,使用时需要换算
    /// </summary>
    public class BattleRoleAttribute
    {
        public int Hp { get; set; } // 生命值
        public int MaxHp { get; set; } // 生命值
        public int Dodge { get; set; } // 闪避
        public int ReactionSpeed { get; set; } // 反应速度
        public int ReconnaissanceRange { get; set; } // 侦查范围
        public int Visibility { get; set; } // 可见度
        public int AttackVisibility { get; set; } // 射击后可见度
        public int EnergyConsumption { get; set; } // 能量币消耗
        public int HorsePower { get; set; } // 马力-引擎功率
        public int ShieldPhysicsValue { get; set; } // 物理护盾
        public int ShieldChemicalValue { get; set; } // 化学护盾
        public string ArmorType { get; set; } // 装甲种类
        public int KineticArmor { get; set; } // 动能装甲厚度 单位/mm
        public int ChemicalArmor { get; set; } // 聚能装甲厚度 单位/mm
        public int OrdinaryRicochet { get; set; } // 普通炮弹 跳弹率
        public int AdvancedRicochet { get; set; } // 高级炮弹 跳弹率
        public int GrenadeRicochet { get; set; } // 榴弹 跳弹率
        public int MovePower { get; set; } // 移动力 -- 单位/米
        public Dictionary<int, WeaponAttribute> Weapons { get; set; } // 武器
        public Dictionary<PartType, PartAttribute> Parts { get; set; } // 部件属性
        public int CurWeaponId = 0; // 当前武器id

        // 切换武器，返回切换后的武器id，
        public int NextWapon()
        {
            if (Weapons.Count >= 2)
            {
                var keys = Weapons.Keys.ToList();
                keys.Sort();
                int currentIndex = keys.IndexOf(CurWeaponId);
                CurWeaponId = keys[(currentIndex + 1) % keys.Count];
            }
            return CurWeaponId;
        }
        
        // 切换弹药，返回切换后的弹药id，
        public int NextShell()
        {
            if (Weapons[CurWeaponId].Shells.Count >= 2)
            {
                var keys = Weapons[CurWeaponId].Shells.Keys.ToList();
                keys.Sort();
                int currentIndex = keys.IndexOf(Weapons[CurWeaponId].CurShellId);
                Weapons[CurWeaponId].CurShellId = keys[(currentIndex + 1) % keys.Count];
            }
            return Weapons[CurWeaponId].CurShellId;
        }


        public WeaponAttribute GetCurWeaponsAttr()
        {
            return Weapons[CurWeaponId];
        }
    }
}
