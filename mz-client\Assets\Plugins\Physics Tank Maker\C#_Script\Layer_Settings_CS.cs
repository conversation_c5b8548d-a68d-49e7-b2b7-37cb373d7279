﻿using System.Collections;
using System.Collections.Generic;
using GameCommon;
using UnityEngine;

namespace ChobiAssets.PTM
{

    public class Layer_Settings_CS
    {
        const int maxLayersNum = 14;

        public static void Layers_Collision_Settings()
        {
            // "BattleColliderLayer.Wheels" settings.
            Physics.IgnoreLayerCollision(BattleColliderLayer.Wheels, BattleColliderLayer.Wheels, true); // Wheels ignore each other.
            Physics.IgnoreLayerCollision(BattleColliderLayer.Wheels, BattleColliderLayer.Body, true); // Wheels ignore MainBody.


            // "BattleColliderLayer.Reinforce" settings.
            for (int i = 0; i <= maxLayersNum; i++)
            {
                Physics.IgnoreLayerCollision(BattleColliderLayer.Reinforce, i, true); // Suspension arms and track reinforce objects ignore all.
            }

            // "BattleColliderLayer.Bullet" settings.
            Physics.IgnoreLayerCollision(BattleColliderLayer.Bullet, BattleColliderLayer.Bullet, true); // Bullets ignore each other.
            Physics.IgnoreLayerCollision(BattleColliderLayer.Bullet, BattleColliderLayer.Wheels, true); // Bullets ignore any wheels.

            // "BattleColliderLayer.Armor_Collider" settings.
            for (int i = 0; i <= maxLayersNum; i++)
            {
                Physics.IgnoreLayerCollision(BattleColliderLayer.Armor_Collider, i, true);
            }
            Physics.IgnoreLayerCollision(BattleColliderLayer.Armor_Collider, BattleColliderLayer.Bullet, false); // "Armor_colliders" collide with only bullets.

            // "BattleColliderLayer.Extra_Collider" settings.
            for (int i = 0; i <= maxLayersNum; i++)
            {
                Physics.IgnoreLayerCollision(BattleColliderLayer.Extra_Collider, i, true);
            }
            Physics.IgnoreLayerCollision(BattleColliderLayer.Extra_Collider, BattleColliderLayer.Extra_Collider, false); // Extra colliders collide with only each other.
        }

    }

}
