﻿using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace GameLogic
{
    // 热成像开关脚本
    public class TestThermalCameraScript : MonoBehaviour
    {
        public ThermalVisionRenderFeature thermalFeature; // 拖入 inspector
        public NightVisionRenderFeature nightFeature;
        public int useTexture = 1;
        public float environmentTemperature = 0.2f;

        private InputAction _spaceAction;
        private InputAction _testProne;
        public bool TV = false;
        public bool NVision;

        private void Start()
        {
            var playerInput = GameObject.Find("CameraContent").GetComponent<PlayerInput>();
            _spaceAction = playerInput.actions["SpaceButton"];
            _testProne = playerInput.actions["TestProne"];
            _spaceAction.started += OnSpaceClick;
            _testProne.started += OnNKeyClick;
        }

        private void OnNKeyClick(InputAction.CallbackContext ctx)
        {
            if (nightFeature == null) return;

            NVision = !NVision;
            nightFeature.settings.isNightVisionOn = NVision;
        }

        private void OnSpaceClick(InputAction.CallbackContext ctx)
        {
            if (thermalFeature == null) return;

            TV = !TV;

            thermalFeature.isThermalOn = TV;
            thermalFeature.useTexture = useTexture;
            thermalFeature.environmentTemperature = environmentTemperature;

            ChangeVisioFlag(TV);
        }

        private static void ChangeVisioFlag(bool isOn)
        {
            var roleList = FindObjectsOfType<RoleVisionSupport>();
            var treeList = FindObjectsOfType<TreeVisionSupport>();
            var tankList = FindObjectsOfType<TankVisionSupport>();

            foreach (var vision in roleList)
            {
                vision.SetRimLight(isOn);
            }

            foreach (var vision in treeList)
            {
                vision.SetTreeVision(isOn);
            }

            foreach (var vision in tankList)
            {
                vision.SetSpecialVision(isOn);
            }
        }

        private void OnDestroy()
        {
            _spaceAction.started -= OnSpaceClick;
            _testProne.started -= OnNKeyClick;
        }
    }
}