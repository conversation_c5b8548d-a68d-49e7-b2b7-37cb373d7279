﻿Shader "Hidden/ThermalVisionPostProcessing"
{
    Properties
    {
        _MainTex("Texture", 2D) = "white" {} // 主渲染纹理（屏幕原始图像）
    }

    SubShader
    {
        // 关闭剔除、深度写入，深度测试总是通过，保证全屏后处理生效
        Cull Off ZWrite On ZTest LEqual

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            // 顶点输入结构体，带顶点位置和UV坐标
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            // 顶点到片元结构体，带UV和屏幕位置
            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            // 采样器定义
            sampler2D _MainTex; // 屏幕渲染的原始颜色纹理
            sampler2D _HeatMapTex; // 热度图纹理，存储温度信息（通常温度存在R通道）
            sampler2D _PaletteTex; // 调色板纹理，用于伪彩色映射

            int useTexture; // 是否使用调色板，0：不使用，其他值代表不同调色板索引
            float3 coolColor; // 伪彩色渐变起始颜色（冷色）
            float3 midColor; // 伪彩色渐变中间颜色
            float3 warmColor; // 伪彩色渐变结束颜色（暖色）

            // 顶点着色器：计算裁剪空间位置和传递UV
            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }

            // 片元着色器：计算最终颜色输出
            float4 frag(v2f i) : SV_Target
            {
                // 采样屏幕原始颜色
                float4 mainSrc = tex2D(_MainTex, i.uv);

                // 采样热度图颜色（温度数据）
                float3 heatColor = tex2D(_HeatMapTex, i.uv).rgb;

                // 计算热度图颜色的亮度（Luminance），用加权RGB求亮度
                float heatLuminance = dot(heatColor, float3(0.299f, 0.587f, 0.114f));

                // 计算屏幕原始颜色的亮度
                float mainLuminance = dot(mainSrc.rgb, float3(0.299f, 0.587f, 0.114f));

                // 判断该像素是否有热度信息（热度图亮度是否大于阈值）
                bool hasHeat = heatLuminance > 0.01;

                // 亮度值来源于热度图（有热度）或原始图像（无热度）
                float luminance = hasHeat ? heatLuminance : mainLuminance;

                float4 res = 0;

                // 如果使用调色板纹理
                if (useTexture != 0)
                {
                    // 在调色板纹理中根据亮度采样对应颜色
                    // 0.25和0.5的偏移用于切换不同的调色板条带
                    res = tex2D(_PaletteTex, float2(luminance, 0.25 + (useTexture - 1) * 0.5));
                }
                else
                {
                    // 不使用调色板时，做三段线性渐变伪彩色映射
                    float ix = step(0.5f, luminance);
                    // 亮度小于0.5时在coolColor和midColor之间插值
                    float3 range1 = lerp(coolColor, midColor, (luminance - ix * 0.5f) * 2);
                    // 亮度大于等于0.5时在midColor和warmColor之间插值
                    float3 range2 = lerp(midColor, warmColor, (luminance - ix * 0.5f) * 2);
                    // 根据ix选择对应插值结果，实现渐变分段
                    res = float4(lerp(range1, range2, ix), 1);
                }

                return res;
            }
            ENDCG
        }
    }
}