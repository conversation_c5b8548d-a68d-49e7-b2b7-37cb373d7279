﻿using UnityEngine;
using System.Collections;
using UnityEngine.UI;
using UnityEngine.AI;
using System.Collections.Generic;
using TEngine;
using System.Linq;
using GameCommon;

namespace ChobiAssets.PTM
{
    public class AI_CS : MonoBehaviour
    {
        /*
         * This script is attached to the "AI_Core" in the AI tank.
         * This script controls the behavior of the AI tank.
         * This script works in combination with "AI_Settings_CS" in the top object, and "AI_Hand_CS" in the child object.
         * This script requires also "AI_Headquaters_CS" in the scene to get the taget information.
         */


        // User options >>
        // Drive settings.
        public float WayPoint_Radius = 10.0f;
        public float Pivot_Turn_Angle = 45.0f;
        public float Min_Target_Angle = 2.5f;
        public float Min_Turn_Rate = 0.0f;
        public float Max_Turn_Rate = 1.0f;
        public float Min_Speed_Rate = 0.0f;
        public float Max_Speed_Rate = 1.0f;
        public float SlowDown_Range = 15.0f;
        public GameObject NavMeshAgent_Prefab;
        public float Agent_Distance = 5.0f;
        public float Agent_Additional_Distance = 5.0f;
        public GameObject Obstacle_Prefab;

        public float Stuck_Count = 2.0f; // Referred to from "AI_Hand"

        // Combat settings.
        public float Fire_Angle = 2.0f; // Referred to from "Aiming_Control_CS".
        public bool Direct_Fire = true; // Referred to from "Cannon_Fire_CS".
        public float Fire_Count = 0.5f; // Referred to from "Cannon_Fire_CS".
        // << User options


        Transform thisTransform;
        Transform eyeTransform;
        public NavMeshAgent navAgent;
        public Transform navAgentTransform;
        public Aiming_Control_CS aimingScript; // 改为 Public
        public AI_Settings_CS Settings_Script; // Referred to from "AI_Headquaters_CS".
        float navAgentDistance;
        float updateDestinationCount;
        float updateDestinationPeriod = 1.0f;
        public bool Is_Sharing_Target; // Set by "AI_Share_Target_CS", and referred to from "AI_Headquaters_CS" in the scene.

        // Target informations.
        public Transform Target_Transform; // Referred to from "AI_Share_Target_CS".
        public Transform targetRootTransform;
        float targetUpperOffset;

        // For actions.
        public int Action_Type; // 0 = Defensive, 1 = Offensive. // Referred to from "UI_PosMarker_Control_CS" and "UI_AIState_Control_CS".
        int defensiveType; // 0 = Waypoint mode, 1 = Follow mode.
        public Transform[] wayPoints;
        int nextWayPoint = -1;
        Vector3 lookAtPosition;
        bool isStaying = false;
        Transform followBodyTransform;
        float followDistance = 15.0f;

        // For searching the target.
        float searchingCount;
        float searchingInterval = 1.0f;
        float targetDistance;
        public bool Detect_Flag; // Referred to from "UI_AIState_Control_CS", "AI_Headquaters_CS".
        public float Losing_Count; // Referred to from "UI_AIState_Control_CS".
        public bool Wakeful_Flag;

        // 视野检测相关 (Field of view detection)
        [System.Serializable]
        public class FieldView
        {
            [Range(5, 2000)] public float Radious;
            [Range(1, 360)] public float Angle;

            public FieldView(float radious, float angle)
            {
                Radious = radious;
                Angle = angle;
            }

            // 检查视野内的碰撞体 (Check colliders within field of view)
            public Collider[] CheckViewCollider(Vector3 position, Vector3 forward, LayerMask targetMask, GameObject viewerToIgnore = null)
            {
                List<Collider> colliders = Physics.OverlapSphere(position, Radious, targetMask).ToList();

                foreach (Collider col in colliders)
                {
                    Transform target = col.transform;

                    Vector3 targetposition = target.position;
                    targetposition.y = position.y;

                    Vector3 directionToTarget = (targetposition - position).normalized;

                    if (Vector3.Angle(forward, directionToTarget) > Angle / 2 || col.gameObject == viewerToIgnore)
                    {
                        colliders.Remove(col);
                    }
                }

                return colliders.ToArray();
            }

            // 检查目标是否在视野内可见 (Check if target is visible within field of view)
            public bool IsVisibleToThisFieldOfView(Transform LookedTarget, Vector3 ViewPosition, Vector3 ViewForward, LayerMask LayerMask, string[] TagsToConsiderVisible = null)
            {
                if (LookedTarget == null) return false;

                bool CanSeeTarget = true;
                Vector3 directionToTarget = (LookedTarget.position - ViewPosition).normalized;

                // 目标不在视角范围内 (Target is not within view angle)
                if (Vector3.Angle(ViewForward, directionToTarget) > Angle / 2)
                {
                    CanSeeTarget = false;
                }
                else
                {
                    float normalDistance = Vector3.Distance(ViewPosition, LookedTarget.position);
                    Vector3 lineCastEndPosition = ViewPosition + directionToTarget * normalDistance;

                    RaycastHit hit;
                    if (Physics.Linecast(ViewPosition, lineCastEndPosition, out hit, LayerMask))
                    {
                        if (hit.collider != null)
                        {
                            // 检查是否击中目标 (Check if hit the target)
                            if (hit.transform.root != LookedTarget.root)
                            {
                                Debug.DrawLine(hit.point, ViewPosition, Color.cyan);
                                CanSeeTarget = false;
                            }
                            else
                            {
                                CanSeeTarget = true;
                            }
                        }
                    }
                }

                return CanSeeTarget;
            }
        }

        public FieldView fieldOfView = new FieldView(10, 60); // 视野范围和角度
        public string[] targetTags = new string[] { "Player" }; // 目标标签
        public LayerMask sensorLayerMask; // 传感器层掩码
        private bool currentTargetIsVisible; // 当前目标是否可见
        private Vector3 fieldViewPosition; // 视野位置
        int Wake_Up_Timer_ID;

        // For offensive actions.
        public bool OpenFire_Flag; // Referred to from "Cannon_Fire_CS".
        bool GunsCanAim;
        Bullet_Generator_CS[] bulletGeneratorScripts;
        float castRayCount;
        bool hasApproached;

        // For driving.
        AI_Hand_CS handScript;
        bool isEscapingFromStuck = false;
        float drivingTargetAngle; // 用于存储坦克需要转向的目标角度
        float drivingTargetDistance; // 用于存储坦克需要转向的目标角度
        float drivingNextCornerAngle; // 用于存储坦克需要转向的下一个拐角角度
        public float Speed_Order; // Referred to from "Drive_Control_CS".
        public float Turn_Order; // Referred to from "Drive_Control_CS".
        float currentMaxSpeedRate;

        // For navigation.
        Drive_Control_CS driveControlScript;


        // public float NavAgentSpeed = 64.0f;
        public float NavAgentSpeed = 15.0f;

        private Damage_Control_Center_CS destoryControlScript;


        void Awake()
        {
            // Broadcast this reference to all the tank parts from the top object.
            transform.parent.parent.BroadcastMessage("Get_AI_CS", this, SendMessageOptions.DontRequireReceiver);
        }


        void Start()
        {
            // MessageHandler.Instance.AddListener(1, CreateObjectOnGround);
            Initialize();
        }

        private void CreateObjectOnGround(List<object> param)
        {
            Vector3 pTf = (Vector3)param[0];
            wayPoints[0].position = pTf;
        }


        void Initialize()
        {
            thisTransform = transform;

            // Get "AI_Settings_CS" in the top object.
            Settings_Script = GetComponentInParent<AI_Settings_CS>();
            if (Settings_Script == null)
            {
                Debug.LogError("'AI_CS' requires 'AI_Settings_CS' in the top object.");
                Destroy(this);
                return;
            }

            // Get "Aiming_Control_CS".
            aimingScript = GetComponentInParent<Aiming_Control_CS>();

            destoryControlScript = GetComponentInParent<Damage_Control_Center_CS>();

            // Get "AI_Eye"
            eyeTransform = thisTransform.Find("AI_Eye"); // (Note.) Do not rename "AI_Eye".
            if (eyeTransform == null)
            {
                Debug.LogError("'AI_Eye' can not be found. ");
                Destroy(this);
            }

            // Get "AI_Hand_CS" script.
            handScript = thisTransform.GetComponentInChildren<AI_Hand_CS>();

            // Get all the "Bullet_Generator_CS" in the tank.
            bulletGeneratorScripts = thisTransform.parent.GetComponentsInChildren<Bullet_Generator_CS>();

            // 初始化视野检测相关设置 (Initialize field of view detection settings)
            fieldOfView.Radious = Settings_Script.Visibility_Radius;
            fieldOfView.Angle = 120f; // 视野角度设为120度
            sensorLayerMask = BattleColliderLayer.Layer_Mask;

            // 玩家人形单位nav放在根节点上
            // var isc = thisTransform.parent.parent.GetComponent<ID_Settings_CS>();
            // if (isc.Relationship == 0)
            // {
            //     navAgent = thisTransform.parent.gameObject.GetOrAddComponent<NavMeshAgent>();
            //     navAgentTransform = thisTransform.parent;
            //     // navAgent.acceleration = 120.0f;
            //     navAgent.acceleration = 10.0f;
            // }
            // else
            // {
            GameObject agentObject;
            if (NavMeshAgent_Prefab)
            {
                agentObject = Instantiate(NavMeshAgent_Prefab, thisTransform.position, thisTransform.rotation, thisTransform.parent.parent) as GameObject;
                navAgent = agentObject.GetComponent<NavMeshAgent>();
            }
            else
            {
                agentObject = new GameObject("AI_NavMeshAgent_Object");
                agentObject.transform.SetPositionAndRotation(thisTransform.position, thisTransform.rotation);
                agentObject.transform.parent = thisTransform.parent.parent;
                navAgent = agentObject.AddComponent<NavMeshAgent>();
            }

            navAgentTransform = agentObject.transform;
            navAgent.acceleration = 120.0f;
            // }

            // 'Follow_Target' settings.
            if (Settings_Script.Follow_Target)
            {
                Get_Follow_Transform();
            }
            else
            {
                //  // The "Follow_Target" is not set.
                // Set up the waypoints.
                Set_WayPoints();
                // Set the first waypoint.
                Update_Next_WayPoint();
            }

            // 'AI_State_Text' settings.
            if (Settings_Script.AI_State_Text)
            {
                UI_AIState_Control_CS stateScript = Settings_Script.AI_State_Text.GetComponent<UI_AIState_Control_CS>();
                if (stateScript == null)
                {
                    stateScript = Settings_Script.AI_State_Text.gameObject.AddComponent<UI_AIState_Control_CS>();
                }

                stateScript.Get_AI_Script(this);
            }

            // 'Commander' settings.
            if (Settings_Script.Commander)
            {
                AI_Share_Target_CS shareTargetScript = gameObject.AddComponent<AI_Share_Target_CS>();
                shareTargetScript.AI_Script = this;
            }

            // Get the "Drive_Control_CS".
            driveControlScript = GetComponentInParent<Drive_Control_CS>();

            // Set the current max speed rate.
            currentMaxSpeedRate = Max_Speed_Rate * Settings_Script.Patrol_Speed_Rate;


            GameEvent.AddEventListener(ActorEventDefine.BattleUnitWakeUp, OnBattleUnitWakeUp);
        }

        private void OnBattleUnitWakeUp()
        {
            StartCoroutine("Wake_Up_Timer");
        }

        void OnDestroy()
        {
            GameEvent.RemoveEventListener(ActorEventDefine.BattleUnitWakeUp, OnBattleUnitWakeUp);
        }

        void Set_WayPoints()
        {
            if (Settings_Script.WayPoint_Pack)
            {
                int childCount = Settings_Script.WayPoint_Pack.transform.childCount;
                if (childCount > 1)
                {
                    // The "WayPoint_Pack" has more than two waypoints.
                    wayPoints = new Transform[childCount];
                    for (int i = 0; i < childCount; i++)
                    {
                        wayPoints[i] = Settings_Script.WayPoint_Pack.transform.GetChild(i);
                    }

                    return;
                }
                else if (childCount == 1)
                {
                    // The "WayPoint_Pack" has only one waypoint.
                    wayPoints = new Transform[1];
                    wayPoints[0] = Settings_Script.WayPoint_Pack.transform.GetChild(0);
                    return;
                }
            }
            // The "WayPoint_Pack" has no point, or is not assigined.

            // Create a new waypoint.
            wayPoints = new Transform[1];
            var newWayPoint = thisTransform.root.Find("Waypoint (1)");
            if (newWayPoint == null)
            {
                newWayPoint = new GameObject("Waypoint (1)").transform;
            }

            newWayPoint.parent = thisTransform.root;
            newWayPoint.position = thisTransform.position;
            newWayPoint.rotation = thisTransform.rotation;
            wayPoints[0] = newWayPoint;
        }


        void Get_Follow_Transform()
        {
            // This function is called also when the follow target tank has been respawned.
            if (Settings_Script.Follow_Target == null)
            {
                // The "Follow_Target" might have been removed from the scene.
                // Change to waypoint mode.
                defensiveType = 0; // Waypoint mode.
                isStaying = false;
                // Set up the waypoints.
                Set_WayPoints();
                // Set the first waypoint.
                Update_Next_WayPoint();
                return;
            }

            // Get the MainBody of the follow target tank.
            Rigidbody followRigidbody = Settings_Script.Follow_Target.GetComponentInChildren<Rigidbody>();
            if (followRigidbody)
            {
                // The "Follow_Target" has a rigidbody. >> The rigidbody should be in the MainBody of the follow target tank.
                defensiveType = 1; // Follow mode.
                // Assign the MainBody of the follow target tank to the "followBodyTransform".
                followBodyTransform = followRigidbody.transform;
            }
            else
            {
                // The "Follow_Target" has no rigidbody.
                Debug.LogError("'Follow Target' has no rigidbody. AI cannot follow the target tank.");
                // Change to waypoint mode.
                Update_Next_WayPoint();
            }
        }


        void Update_Next_WayPoint()
        {
            switch (Settings_Script.Patrol_Type)
            {
                case 0: // Order
                    nextWayPoint += 1;
                    if (nextWayPoint >= wayPoints.Length)
                    {
                        nextWayPoint = 0;
                    }

                    break;

                case 1: // Random
                    nextWayPoint = Random.Range(0, wayPoints.Length);
                    break;
            }

            // Update the destination of the NavMeshAgent.
            SetDestinationWithSamplePosition(wayPoints[nextWayPoint].position);
        }

        /// <summary>
        /// 让角色暂停移动
        /// </summary>
        public void ActionMovePause()
        {
            IsPlayerMove = false;
            navAgent.isStopped = false;
            // navAgent.ResetPath(); // 来清除路径
        }

        /// <summary>
        /// 让角色继续移动
        /// </summary>
        public void ActionMoveResume()
        {
            IsPlayerMove = true;
            navAgent.isStopped = true;
        }

        /// <summary>
        /// 行动到目标点
        /// </summary>
        /// <param name="_targetPos"></param>
        public void ActionMoveTo(Vector3 _targetPos)
        {
            wayPoints[0].position = _targetPos;
            navAgent.SetDestination(_targetPos);
        }

        void Update()
        {
            if (!IsPlayerMove)
                return;

            // Control the NavMeshAgent.
            navAgentDistance = Vector3.Distance(navAgentTransform.position, thisTransform.position);
            if (navAgentDistance > Agent_Distance + Agent_Additional_Distance)
            {
                // The NavMeshAgent is too far.
                // Reset the position.
                navAgent.nextPosition = thisTransform.position;
            }
            else
            {
                // Control the speed to keep the distance from the tank.
                float tempRate = Mathf.Pow(driveControlScript.Current_Velocity / driveControlScript.Max_Speed, 2.0f);
                tempRate = Mathf.Clamp(tempRate, 0.0f, 1.0f);
                navAgent.speed = Mathf.Lerp(NavAgentSpeed, 0.0f, navAgentDistance / (Agent_Distance + (Agent_Additional_Distance * tempRate)));
            }

            // 搜索目标 (Search target)
            // Search_Target();

            // 更新视野位置 (Update field of view position)
            fieldViewPosition = eyeTransform.position;

            // Action.
            switch (Action_Type)
            {
                case 0: // Defensive.
                    WayPoint_Mode();
                    break;

                case 1: // Offensive.
                    if (Target_Transform)
                    {
                        // The target exists.
                        if (Settings_Script.Breakthrough == true)
                        {
                            // Keep going around the waypoaints while attacking the target.
                            Breakthrough_Mode();
                        }
                        else
                        {
                            // Chase the target while attaking it.
                            Chase_Mode();
                        }
                    }
                    else
                    {
                        // The target does not exist. >> The target might be respawned, or be removed from the scene.
                        Lost_Target();
                        return;
                    }

                    break;
            }

            // Auto brake function.
            if (handScript && handScript.Is_Working)
            {
                // An obstacle is detected in fornt of the tank.
                drivingTargetDistance = 0.0f; // The tank will stop, and will be allowed only to turn.
            }

            // Calculate the "Speed_Order" and "Turn_Order" for driving.
            Auto_Drive();

            // if (navAgent.destination != tempDestination)
            // {
            //     tempDestination = navAgent.destination;
            //     Debug.Log("目的地: " + tempDestination);
            // }

            // navAgent.isStopped = !IsPlayerMove;
        }


        // 主动搜索目标 (Actively search for targets)
        void ActiveSearchTargets()
        {
            // 设置视野位置 (Set field of view position)
            fieldViewPosition = eyeTransform.position;

            // 使用视野检测获取周围的目标 (Use field of view to detect surrounding targets)
            Collider[] potentialTargets = fieldOfView.CheckViewCollider(fieldViewPosition, thisTransform.forward, sensorLayerMask, this.gameObject);

            // 如果找到了潜在目标 (If potential targets are found)
            if (potentialTargets.Length > 0)
            {
                // 从目标列表中选择一个目标 (Select a target from the list)
                Transform newTarget = null;

                foreach (Collider col in potentialTargets)
                {
                    // 检查目标标签是否匹配 (Check if target tag matches)
                    foreach (string tag in targetTags)
                    {
                        if (col.CompareTag(tag))
                        {
                            newTarget = col.transform;
                            break;
                        }
                    }

                    if (newTarget != null)
                        break;
                }

                // 如果找到了有效目标 (If a valid target is found)
                if (newTarget != null)
                {
                    // 如果当前没有目标或者找到了新目标 (If currently no target or found a new target)
                    if (Target_Transform == null || Target_Transform != newTarget)
                    {
                        // 设置新目标 (Set new target)
                        Target_Transform = newTarget;
                        targetRootTransform = newTarget.root;
                        targetUpperOffset = 1.0f; // 默认上偏移量 (Default upper offset)

                        // 锁定目标 (Lock on target)
                        if (aimingScript)
                        {
                            aimingScript.AI_Lock_On(Target_Transform);
                        }

                        // 设置为进攻状态 (Set to offensive state)
                        Action_Type = 1;
                        if (Settings_Script.Breakthrough == false)
                        {
                            // 不是突破模式 >> 追击目标 (Not breakthrough mode >> Chase target)
                            isStaying = false;
                            // 移动NavMeshAgent到当前位置，使其能平滑找到新路径 (Move NavMeshAgent to current position for smooth path finding)
                            navAgent.Warp(thisTransform.position);
                            // 设置updateDestinationCount，使NavMeshAgent目的地能尽快更新 (Set updateDestinationCount for quick destination update)
                            updateDestinationCount = Mathf.Infinity;
                            // 改变当前最大速度比率 (Change current max speed rate)
                            currentMaxSpeedRate = Max_Speed_Rate * Settings_Script.Combat_Speed_Rate;
                        }

                        // 设置检测标志 (Set detection flag)
                        Detect_Flag = true;
                        Losing_Count = Settings_Script.Lost_Count;
                    }
                }
            }
        }

        // 检查当前目标 (Check current target)
        void CheckCurrentTarget()
        {
            // 如果没有目标，直接返回 (If no target, return directly)
            if (Target_Transform == null)
                return;

            // 设置视野位置 (Set field of view position)
            fieldViewPosition = eyeTransform.position;

            // 检查目标状态 (Check target state)
            if (targetRootTransform.CompareTag("Finish"))
            {
                // 目标已经死亡 (Target is already dead)
                Lost_Target();
                return;
            }

            // 检测目标 (Detect target)
            Vector3 targetPosition = Target_Transform.position + (Target_Transform.up * targetUpperOffset);
            targetDistance = Vector3.Distance(fieldViewPosition, targetPosition);

            // 检查目标是否在死角 (Check if target is in dead angle)
            bool inDeadAngle = false;
            if (Action_Type == 0 && !Wakeful_Flag)
            {
                // 防御状态且未被攻击 (Defensive and not attacked)
                // 获取到目标的角度 (Get angle to target)
                float theta = Mathf.Abs(Calculate_2D_Angle(targetPosition));
                if (180.0f - theta < Settings_Script.Dead_Angle)
                {
                    // 目标在死角内 (Target is in dead angle)
                    inDeadAngle = true;
                }
            }

            // 使用视野检测目标 (Use field of view to detect target)
            if (targetDistance < fieldOfView.Radious || Wakeful_Flag || Is_Sharing_Target)
            {
                if (inDeadAngle && !Wakeful_Flag && !Is_Sharing_Target)
                {
                    // 目标在死角内且未被攻击且未共享目标 (Target is in dead angle, not attacked, and not sharing target)
                    currentTargetIsVisible = false;
                    UpdateTargetNotDetected();
                }
                else
                {
                    // 目标在视野范围内，或者刚被攻击，或者共享目标 (Target is within field of view, or just attacked, or sharing target)
                    currentTargetIsVisible = fieldOfView.IsVisibleToThisFieldOfView(Target_Transform, fieldViewPosition,
                        thisTransform.forward, sensorLayerMask) || Is_Sharing_Target;

                    if (currentTargetIsVisible)
                    {
                        // 目标被检测到 (Target is detected)
                        Detect_Flag = true;
                        UpdateTargetDetected();
                    }
                    else
                    {
                        // 目标未被检测到 (Target is not detected)
                        UpdateTargetNotDetected();
                    }
                }
            }
            else
            {
                // 目标超出视野范围 (Target is out of field of view)
                currentTargetIsVisible = false;
                UpdateTargetNotDetected();
            }
        }

        // 目标被检测到时的处理 (Processing when target is detected)
        void UpdateTargetDetected()
        {
            switch (Action_Type)
            {
                case 0: // 防御状态 (Defensive)
                    Action_Type = 1;
                    if (Settings_Script.Breakthrough == false)
                    {
                        // 不是突破模式 >> 追击目标 (Not breakthrough mode >> Chase target)
                        isStaying = false;
                        // 移动NavMeshAgent到当前位置，使其能平滑找到新路径 (Move NavMeshAgent to current position for smooth path finding)
                        navAgent.Warp(thisTransform.position);
                        // 设置updateDestinationCount，使NavMeshAgent目的地能尽快更新 (Set updateDestinationCount for quick destination update)
                        updateDestinationCount = Mathf.Infinity;
                        // 改变当前最大速度比率 (Change current max speed rate)
                        currentMaxSpeedRate = Max_Speed_Rate * Settings_Script.Combat_Speed_Rate;
                    }

                    // 调用"Aiming_Control_CS"锁定目标 (Call "Aiming_Control_CS" to lock on target)
                    if (aimingScript)
                    {
                        aimingScript.AI_Lock_On(Target_Transform);
                    }

                    break;

                case 1: // 进攻状态 (Offensive)
                    // 继续追击目标 (Continue chasing target)
                    Losing_Count = Settings_Script.Lost_Count;
                    break;
            }
        }

        // 目标未被检测到时的处理 (Processing when target is not detected)
        void UpdateTargetNotDetected()
        {
            Detect_Flag = false;
            switch (Action_Type)
            {
                case 0: // 防御状态 (Defensive)
                    break;

                case 1: // 进攻状态 (Offensive)
                    Losing_Count -= Time.deltaTime + searchingInterval;
                    if (Losing_Count < 0.0f)
                    {
                        // AI已经失去目标 (AI has lost the target)
                        Lost_Target();
                    }

                    break;
            }
        }

        // 原始的搜索目标方法，现在调用新的目标检测方法 (Original search target method, now calls new target detection methods)
        void Search_Target()
        {
            if (Settings_Script.No_Attack)
                return;

            // 检查间隔 (Check interval)
            searchingCount -= Time.deltaTime;
            if (searchingCount > 0.0f)
            {
                return;
            }

            searchingCount = searchingInterval;

            // 如果没有目标，尝试主动搜索 (If no target, try active search)
            if (Target_Transform == null)
            {
                ActiveSearchTargets();
            }
            else
            {
                // 检查当前目标 (Check current target)
                CheckCurrentTarget();
            }
        }


        public IEnumerator Wake_Up_Timer()
        {
            // Called from "Damage_Control_Center_CS", when the AI tank is attacked.
            Wakeful_Flag = true;
            Wake_Up_Timer_ID += 1;
            int thisTimerID = Wake_Up_Timer_ID;

            float count = 0.0f;
            while (count < 5.0f)
            {
                if (thisTimerID != Wake_Up_Timer_ID)
                {
                    // The tank should be attacked again.
                    yield break;
                }

                count += Time.deltaTime;
                yield return null;
            }

            Wake_Up_Timer_ID = 0;
            Wakeful_Flag = false;
        }


        void WayPoint_Mode()
        {
            // Update the destination.
            updateDestinationCount += Time.deltaTime;
            if (updateDestinationCount > updateDestinationPeriod)
            {
                SetDestinationWithSamplePosition(wayPoints[nextWayPoint].position);
                updateDestinationCount = 0.0f;
            }

            // Check the distance to the waypoint.
            if (wayPoints.Length > 1)
            {
                // There are more than two waypoints.
                if (Vector3.Distance(thisTransform.position, wayPoints[nextWayPoint].position) < WayPoint_Radius)
                {
                    // The tank has arriveded at the next waypoint.
                    // Update the next waypoint.
                    Update_Next_WayPoint();
                }
                else
                {
                    // The tank does not arrive at the next waypoint.
                    // Move to the NavMeshAgent.
                    Set_Driving_Target_Angle_And_Distance();
                }
            }
            else
            {
                // There is only one waypoint.
                float distanceToWaypoint = Vector3.Distance(thisTransform.position, wayPoints[0].position);
                if (isStaying)
                {
                    // The tank is staying now.
                    if (distanceToWaypoint > WayPoint_Radius + 5.0f)
                    {
                        // The tank has moved away from the waypoint.
                        isStaying = false;
                    }
                }
                else
                {
                    // The tank is not staying now.
                    if (distanceToWaypoint < WayPoint_Radius)
                    {
                        // The tank has arrived at the waypoint.
                        // Set the "lookAtPosition".
                        lookAtPosition = wayPoints[0].position + (wayPoints[0].forward * 100.0f);
                        isStaying = true;
                    }
                }

                // Set the "drivingTargetAngle" and "drivingTargetDistance".
                if (isStaying)
                {
                    // The tank is near the waypoint.
                    // Face the same direction as the waypoint.
                    drivingTargetAngle = Calculate_2D_Angle(lookAtPosition);
                    // Stay.
                    drivingTargetDistance = 0.0f;
                }
                else
                {
                    // The tank is away from the waypoint.
                    // Move to the NavMeshAgent.
                    Set_Driving_Target_Angle_And_Distance();
                }
            }
        }


        void Follow_Mode()
        {
            // Check the follow target exists.
            if (followBodyTransform == null)
            {
                Get_Follow_Transform(); // The "followBodyTransform" might have been lost by respawning.
                return;
            }

            // Update the destination.
            updateDestinationCount += Time.deltaTime;
            if (updateDestinationCount > updateDestinationPeriod)
            {
                SetDestinationWithSamplePosition(followBodyTransform.position);
                updateDestinationCount = 0.0f;
            }

            // Check the distance to the follow target.
            float distanceToFollowTatget = Vector3.Distance(thisTransform.position, followBodyTransform.position);
            if (isStaying)
            {
                // The tank is staying now.
                if (distanceToFollowTatget < followDistance + 5.0f)
                {
                    // The tank is staying near the target.
                    // Keep staying.
                    isStaying = true;
                    // Update the "lookAtPosition", so that the tank faces the same direction as the follow target.
                    lookAtPosition = followBodyTransform.position + (followBodyTransform.forward * 100.0f);
                    // Hold the NavMeshAgent in this position, so that the tank smoothly start following again.
                    navAgent.Warp(thisTransform.position);
                }
                else
                {
                    // The tank has moved away from the follow target.
                    isStaying = false;
                }
            }
            else
            {
                // The tank is not staying now.
                if (distanceToFollowTatget < followDistance)
                {
                    // The tank has arrived at near the follow target.
                    isStaying = true;
                    // Set the "lookAtPosition", so that the tank faces the same direction as the follow target.
                    lookAtPosition = followBodyTransform.position + (followBodyTransform.forward * 100.0f);
                }
            }

            // Set the "drivingTargetAngle" and "drivingTargetDistance".
            if (isStaying)
            {
                // The tank is staying near the target.
                // Face the same direction as the follow target.
                drivingTargetAngle = Calculate_2D_Angle(lookAtPosition);
                // Stay.
                drivingTargetDistance = 0.0f;
            }
            else
            {
                // The tank is away from the follow target.
                // Move to the NavMeshAgent.
                Set_Driving_Target_Angle_And_Distance();
                if (navAgent.path.corners.Length < 3)
                {
                    // The next corner position must be the target position.
                    // Reduce the target distance so that the tank can follow the target smoothly.
                    drivingTargetDistance -= 15.0f;
                    drivingNextCornerAngle = 180.0f;
                }
            }
        }


        void Breakthrough_Mode()
        {
            // Keep the behavior in the defensive condition.
            switch (defensiveType)
            {
                case 0: // Waypoint mode.
                    WayPoint_Mode();
                    break;
                case 1: // Follow mode.
                    Follow_Mode();
                    break;
            }

            // Set the "OpenFire_Flag" referred to from "Cannon_Fire_CS".
            if (Detect_Flag == true)
            {
                // The target is detected.
                // Check the target is within the "OpenFire_Distance" or not.
                OpenFire_Flag = (targetDistance < Settings_Script.OpenFire_Distance);
            }
            else
            {
                // The target is not detected.
                OpenFire_Flag = false;
            }

            // Set "Can_Aim" in all the "Bullet_Generator_CS" referred to from "Cannon_Fire_CS".
            if (OpenFire_Flag == true)
            {
                // The target is detected and is within the "OpenFire_Distance"
                if (Direct_Fire == true)
                {
                    // The tank aims a target directly.
                    castRayCount += Time.fixedDeltaTime;
                    if (castRayCount > 2.0f)
                    {
                        castRayCount = 0.0f;
                        Set_Can_Aim();
                    }
                }
            }
        }


        void Chase_Mode()
        {
            // Update the destination.
            updateDestinationCount += Time.deltaTime;
            if (updateDestinationCount > updateDestinationPeriod)
            {
                // 只有当未接近目标时才更新目的地
                if (!hasApproached)
                {
                    SetDestinationWithSamplePosition(Target_Transform.position);
                }

                updateDestinationCount = 0.0f;
            }

            // Set the "OpenFire_Flag" referred to from "Cannon_Fire_CS".
            if (Detect_Flag == true)
            {
                // The target is detected.
                // Check the target is within the "OpenFire_Distance" or not.
                OpenFire_Flag = (targetDistance < Settings_Script.OpenFire_Distance);
            }
            else
            {
                // The target is not detected.
                OpenFire_Flag = false;
            }

            // Check all the guns can aim the target or not.
            if (OpenFire_Flag == true)
            {
                // The target is detected and is within the "OpenFire_Distance"
                if (Direct_Fire == true)
                {
                    // The tank aims a target directly.
                    castRayCount += Time.fixedDeltaTime;
                    if (castRayCount > 2.0f)
                    {
                        castRayCount = 0.0f;
                        GunsCanAim = Set_Can_Aim();
                    }
                }
                else
                {
                    // The tank aims a target indirectly like a howitzer.
                    GunsCanAim = true;
                }
            }
            else
            {
                // The target is not detected.
                GunsCanAim = false;
            }

            // Check the tank is within the "Approach_Distance" or not.
            if (targetDistance < Settings_Script.Approach_Distance)
            {
                // The target is within the "Approach_Distance".
                if (GunsCanAim || float.IsPositiveInfinity(Settings_Script.Approach_Distance))
                {
                    // At least one of the guns can aim the target, or the "Approach_Distance" is set to infinity.
                    hasApproached = true;

                    // 当接近目标并可以瞄准时，停止导航代理移动
                    if (navAgent && navAgent.enabled)
                    {
                        navAgent.isStopped = true;
                    }
                }
                else
                {
                    // Any gun can not aim the tagert.
                    hasApproached = false;

                    // 恢复导航代理移动
                    if (navAgent && navAgent.enabled)
                    {
                        navAgent.isStopped = false;
                    }
                }
            }
            else
            {
                // The target is out of 'Approach_Distance'.
                hasApproached = false;

                // 恢复导航代理移动
                if (navAgent && navAgent.enabled)
                {
                    navAgent.isStopped = false;
                }
            }

            // Set the "drivingTargetAngle" and "drivingTargetDistance".
            if (hasApproached)
            {
                // The tank has approached the target and can aim it.
                // Stay.
                drivingTargetDistance = 0.0f;

                // Get the angle to the target.
                drivingTargetAngle = Calculate_2D_Angle(Target_Transform.position);
                if (Settings_Script.Approach_Distance != Mathf.Infinity && Mathf.Abs(drivingTargetAngle) < 60.0f)
                {
                    // The target is almost in front.
                    // Need not to face the target.
                    drivingTargetAngle = 0.0f;
                }
                else
                {
                    // Face the target.
                    drivingTargetAngle -= Settings_Script.Face_Offest_Angle * Mathf.Sign(drivingTargetAngle);
                }
            }
            else
            {
                // The tank has not approached the target yet, or cannot aim it.
                // Move to the NavMeshAgent.
                Set_Driving_Target_Angle_And_Distance();
            }
        }


        bool Set_Can_Aim()
        {
            // Set "Can_Aim" in all the "Bullet_Generator_CS" referred to from "Cannon_Fire_CS".
            // And check at least one of the guns can aim the target or not.
            bool flag = false;
            for (int i = 0; i < bulletGeneratorScripts.Length; i++)
            {
                if (bulletGeneratorScripts[i] == null)
                {
                    continue;
                }

                // Cast a line from the "Bullet_Generator" to the target.
                RaycastHit raycastHit;
                if (Physics.Linecast(bulletGeneratorScripts[i].transform.position, aimingScript.Target_Position, out raycastHit, BattleColliderLayer.Layer_Mask))
                {
                    if (raycastHit.transform.root == targetRootTransform)
                    {
                        // The line hits the target.
                        bulletGeneratorScripts[i].Can_Aim = true;
                        flag = true; // At least one of the "Bullet_Generator" can aim the target.
                    }
                    else
                    {
                        // The line hits other object.
                        bulletGeneratorScripts[i].Can_Aim = false;
                    }
                }
                else
                {
                    // The line does not hit anyhing. >> There is no obstacle between the muzzle and the target.
                    bulletGeneratorScripts[i].Can_Aim = true;
                    flag = true; // At least one of the "Bullet_Generator" can aim the target.
                }
            }

            return flag; // At least one of the "Bullet_Generator" can aim the target or not.
        }


        float Calculate_2D_Angle(Vector3 targetPosition)
        {
            // Calculate the angle to the target for driving.
            Vector3 localPosition3D = thisTransform.InverseTransformPoint(targetPosition);
            Vector2 localPosition2D;
            localPosition2D.x = localPosition3D.x;
            localPosition2D.y = localPosition3D.z;
            return Vector2.Angle(Vector2.up, localPosition2D) * Mathf.Sign(localPosition2D.x);
        }


        void Set_Driving_Target_Angle_And_Distance()
        {
            // Check the state of the NavMeshAgent.
            if (navAgent.path.corners.Length == 0)
            {
                // Something wrong in the NavMeshAgent.
                // Do not move.
                drivingTargetAngle = 0.0f;
                drivingTargetDistance = 0.0f;
                return;
            }

            // Get the angle to the NavMeshAgent.
            drivingTargetAngle = Calculate_2D_Angle(navAgentTransform.position);

            // Get the distance to the next corner.
            if (navAgent.path.corners.Length > 1)
            {
                // The corners [0] should be the NavMeshAgent it self.
                drivingTargetDistance = Vector3.Distance(thisTransform.position, navAgent.path.corners[1]);
                if (navAgent.path.corners.Length > 2)
                {
                    // The next corner (corners [1]) should not be the destination.
                    // Get the angle of the corners [1]. (this position >> corners [1] >> corners [2]")
                    Vector3 vecA = thisTransform.position - navAgent.path.corners[1];
                    Vector3 vecB = navAgent.path.corners[2] - navAgent.path.corners[1];
                    float theta = Mathf.Acos(Vector3.Dot(vecA, vecB) / (vecA.magnitude * vecB.magnitude)) * Mathf.Rad2Deg;
                    drivingNextCornerAngle = 180.0f - theta;
                }
                else
                {
                    // The next corner (corners [1]) should be the destination (the next waypoint).
                    drivingNextCornerAngle = 180.0f;
                }
            }
            else
            {
                // The corners [0] should be the destination (the next waypoint).
                drivingTargetDistance = Vector3.Distance(thisTransform.position, navAgent.path.corners[0]);
                drivingNextCornerAngle = 180.0f;
            }
        }


        public void Set_Target(AI_Headquaters_Helper_CS targetAIHelperScript)
        {
            // Called from "AI_Headquaters_CS" in the scene.
            if (Target_Transform == targetAIHelperScript.Body_Transform)
            {
                // The sent target is the same as the current target.
                return;
            }

            // Reset the values.
            Lost_Target();
            Target_Transform = targetAIHelperScript.Body_Transform;
            targetRootTransform = targetAIHelperScript.Body_Transform.root;
            targetUpperOffset = targetAIHelperScript.Visibility_Upper_Offset;
        }


        public void Lost_Target()
        {
            // Called also from "AI_Share_Target_CS".
            if (Settings_Script.Breakthrough == false)
            {
                // Not breakthrough >> The tank is chasing the target until now.
                // Move the NavMeshAgent to this position, so that the NavMeshAgent can find a new path smoothly.
                navAgent.Warp(thisTransform.position);
            }

            // Reset the values.
            Action_Type = 0;
            updateDestinationCount = 0.0f;
            searchingCount = 0.0f;
            Target_Transform = null;
            targetRootTransform = null;
            Detect_Flag = false;
            OpenFire_Flag = false;
            Losing_Count = Settings_Script.Lost_Count;
            hasApproached = false;
            isStaying = false;
            currentMaxSpeedRate = Max_Speed_Rate * Settings_Script.Patrol_Speed_Rate;

            // Call the "Aiming_Control_CS" to lock off the target.
            if (aimingScript)
            {
                aimingScript.AI_Lock_Off();
            }

            if (defensiveType == 0)
            {
                // Waypoint mode.
                // Update the destination of the NavMeshAgent.
                SetDestinationWithSamplePosition(wayPoints[nextWayPoint].position);
            }
        }


        void Auto_Drive()
        {
            // Calculate "Speed_Order" and "Turn_Order".
            float sign = Mathf.Sign(drivingTargetAngle);
            drivingTargetAngle = Mathf.Abs(drivingTargetAngle);
            if (drivingTargetAngle > Min_Target_Angle)
            {
                // Turn.
                if (drivingTargetAngle > Pivot_Turn_Angle)
                {
                    // Pivot turn.
                    Turn_Order = 1.0f * sign;
                    Speed_Order = 0.0f;
                    return;
                }

                // Brake turn.
                Turn_Order = Mathf.Lerp(Min_Turn_Rate, Max_Turn_Rate, drivingTargetAngle / Pivot_Turn_Angle) * sign;
                if (drivingTargetDistance == 0.0f)
                {
                    Speed_Order = 0.0f;
                    return;
                }

                Speed_Order = 1.0f - Mathf.Abs(Turn_Order);
                float currentMinSpeedRate = Mathf.Lerp(Max_Speed_Rate, Min_Speed_Rate, drivingNextCornerAngle / 45.0f);
                Speed_Order *= Mathf.Lerp(currentMinSpeedRate, Max_Speed_Rate, drivingTargetDistance / SlowDown_Range);
                Speed_Order = Mathf.Clamp(Speed_Order, Min_Speed_Rate, currentMaxSpeedRate);
                return;
            }
            else
            {
                // No turn.
                Turn_Order = 0.0f;
                if (drivingTargetDistance == 0.0f)
                {
                    Speed_Order = 0.0f;
                    return;
                }

                float currentMinSpeedRate = Mathf.Lerp(Max_Speed_Rate, Min_Speed_Rate, drivingNextCornerAngle / 30.0f);
                Speed_Order = Mathf.Lerp(currentMinSpeedRate, Max_Speed_Rate, drivingTargetDistance / SlowDown_Range);
                Speed_Order = Mathf.Clamp(Speed_Order, Min_Speed_Rate, currentMaxSpeedRate);
                return;
            }
        }

        public void Escape_From_Stuck()
        {
            // Called from "AI_Hand" when the tank gets stuck.
            // Move the NavMeshAgent to this position, so that the NavMeshAgent can find a new path smoothly.
            navAgent.Warp(thisTransform.position);

            switch (Action_Type)
            {
                case 0: // Defensive.
                    switch (defensiveType)
                    {
                        case 0: // Waypoint mode.
                            if (isEscapingFromStuck == false && Random.Range(0, 3) == 0)
                            {
                                StartCoroutine("Create_NavMeshObstacle_Object");
                            }
                            else
                            {
                                Update_Next_WayPoint();
                            }

                            break;

                        case 1: // Follow mode.
                            // Stop only.
                            return;
                    }

                    break;

                case 1: // Offensive.
                    if (Settings_Script.Breakthrough == true)
                    {
                        // The AI tank never chase the target.
                        switch (defensiveType)
                        {
                            case 0: // Waypoint mode.
                                if (isEscapingFromStuck == false && Random.Range(0, 3) == 0)
                                {
                                    StartCoroutine("Create_NavMeshObstacle_Object");
                                }
                                else
                                {
                                    Update_Next_WayPoint();
                                }

                                break;

                            case 1: // Follow mode.
                                // Stop only.
                                break;
                        }
                    }
                    else
                    {
                        // The AI tank is chasing the target.
                        if (isEscapingFromStuck == false && Random.Range(0, 2) == 0)
                        {
                            StartCoroutine("Create_NavMeshObstacle_Object");
                        }
                    }

                    break;
            }
        }


        IEnumerator Create_NavMeshObstacle_Object()
        {
            if (Obstacle_Prefab == null)
            {
                yield break;
            }

            isEscapingFromStuck = true;

            // Spawn the "Obstacle_Prefab".
            Instantiate(Obstacle_Prefab, thisTransform.position, thisTransform.rotation);

            // Wait.
            yield return new WaitForSeconds(20.0f);

            isEscapingFromStuck = false;
        }


        public bool Check_for_Assigning(AI_Headquaters_Helper_CS targetAIHelperScript)
        {
            // 从"AI_Headquaters_CS"调用，检查坦克是否能检测到目标 (Called from "AI_Headquaters_CS", check if tank can detect target)
            if (targetAIHelperScript == null || targetAIHelperScript.Body_Transform == null)
                return false;

            // 获取目标位置 (Get target position)
            Vector3 tempTargetPosition = targetAIHelperScript.Body_Transform.position + (targetAIHelperScript.Body_Transform.up * targetAIHelperScript.Visibility_Upper_Offset);

            // 计算距离 (Calculate distance)
            float distance = Vector3.Distance(eyeTransform.position, tempTargetPosition);

            // 如果目标在视野范围内 (If target is within field of view)
            if (distance < fieldOfView.Radious)
            {
                // 使用视野检测目标 (Use field of view to detect target)
                return fieldOfView.IsVisibleToThisFieldOfView(targetAIHelperScript.Body_Transform, eyeTransform.position,
                    thisTransform.forward, sensorLayerMask);
            }

            // 目标超出视野范围 (Target is out of field of view)
            return false;
        }


        public void Reset_Settings()
        {
            // Called from "AI_Settings_CS", after the AI settings has been changed by events.
            if (Settings_Script.Follow_Target)
            {
                Get_Follow_Transform();
            }
            else
            {
                // Set up the waypoints.
                Set_WayPoints();
                // Set the first waypoint.
                nextWayPoint = -1;
                Update_Next_WayPoint();
            }

            Lost_Target();
        }


        private bool _isOnceDead = false;

        void MainBody_Destroyed_Linkage()
        {
            // Called from "Damage_Control_Center_CS".
            if (!_isOnceDead)
            {
                _isOnceDead = true;
                destoryControlScript.MainBody_Destroyed();
            }
            else
            {
                Destroy(navAgent.gameObject);
                Destroy(this.gameObject);
            }
        }


        void Pause(bool isPaused)
        {
            // Called from "Game_Controller_CS".
            this.enabled = !isPaused;
        }


        public bool IsPlayerMove = true;
        float searchRadius = 5f; // 增大搜索半径

        private void SetDestinationWithSamplePosition(Vector3 targetPosition)
        {
            // 增加搜索半径和调试信息
            Debug.DrawLine(transform.position, targetPosition, Color.magenta, 2f); // 可视化目标点连线

            if (NavMesh.SamplePosition(targetPosition, out var hit, searchRadius, NavMesh.AllAreas))
            {
                Debug.DrawLine(targetPosition, hit.position, Color.green, 2f); // 可视化采样结果
                navAgent.SetDestination(hit.position);
                // Debug.Log($"找到有效位置: {hit.position}, 距离目标: {Vector3.Distance(targetPosition, hit.position)}");
            }
            else
            {
                // Debug.LogWarning($"无法找到有效位置。开始搜寻新位置 目标位置: {targetPosition}, 当前位置: {transform.position}");

                // 尝试从当前位置开始搜索
                if (NavMesh.SamplePosition(transform.position, out var currentHit, searchRadius, NavMesh.AllAreas))
                {
                    navAgent.Warp(currentHit.position); // 确保当前位置有效
                    navAgent.SetDestination(currentHit.position);
                    // Debug.Log($"回退到当前位置附近的有效点: {currentHit.position}");
                }
                else
                {
                    Debug.LogWarning($"当前位置附近也没有有效的NavMesh点! [{targetPosition.x}:{targetPosition.z}]");
                }
            }
        }
    }
}