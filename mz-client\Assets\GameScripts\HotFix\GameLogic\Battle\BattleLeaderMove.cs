﻿using Cinemachine;
using JUTPS;
using TEngine;
using UnityEngine;
using UnityEngine.PlayerLoop;

namespace GameLogic
{
    public enum LeaderMoveState
    {
        Running,
        NormalMove,
        Crouching,
        Stop
    }

    public class BattleLeaderMove : MonoBehaviour
    {
        private UnitActor _unitActor;
        private JUCharacterController _controller;
        private Camera _camera;

        public void Initialize()
        {
            _camera = CinemachineCore.Instance.GetActiveBrain(0).OutputCamera;

            GameEvent.AddEventListener(GameCommon.ActorEventDefine.TestLeaderMoveStart, StartMove);
            GameEvent.AddEventListener(GameCommon.ActorEventDefine.TestLeaderMoveStop, StopMove);
            GameEvent.AddEventListener(GameCommon.ActorEventDefine.TestSetLeader, SetLeader);
            GameEvent.AddEventListener<LeaderMoveState>(GameCommon.ActorEventDefine.TestSetLeaderMoveState, SetLeaderMoveState);
        }

        private bool isMoving = false;
        private bool canMove = true;

        private void Update()
        {
            if (!isMoving || !canMove) return;

            var inputVector = JoystickController.JoystickDirection;
            if (inputVector == Vector2.zero) return;
            Move(inputVector);
        }

        private void SetLeaderMoveState(LeaderMoveState moveState)
        {
            canMove = moveState != LeaderMoveState.Stop;
            isRunning = moveState switch
            {
                LeaderMoveState.Running => true,
                LeaderMoveState.Crouching => false,
                _ => isRunning,
            };
            if (canMove == false)
            {
                StopMove();
            }
        }

        private void SetLeader()
        {
            _unitActor = BattleSystem.Instance.GetLeader();
            _controller = _unitActor.CharacterController;
        }

        private bool isRunning = true;

        private void Move(Vector2 inputDir)
        {
            if (_camera == null || _unitActor == null)
                return;

            // 获取摄像机正前和右方向（忽略y分量）
            var camForward = _camera.transform.forward;
            var camRight = _camera.transform.right;

            // 保证在水平面上移动
            camForward.y = 0;
            camRight.y = 0;
            camForward.Normalize();
            camRight.Normalize();

            // 根据摇杆输入和摄像机方向计算世界空间方向
            var moveDir = camForward * inputDir.y + camRight * inputDir.x;
            moveDir.Normalize(); // 可选，避免极小误差影响

            // Debug.Log($"移动方向 --- [{moveDir}]");
            // 将世界方向转换成角色局部方向（如果你用角度控制，可以跳过）
            _controller._Move(moveDir.x, moveDir.z, isRunning); // 注意: _Move 是 (x, y)，z作为y轴
        }

        private void StartMove()
        {
            isMoving = true;
            _unitActor.AI.StopAI();
        }

        private void StopMove()
        {
            isMoving = false;
            _controller._Move(0, 0, false);
        }

        private void OnDestroy()
        {
            GameEvent.RemoveEventListener(GameCommon.ActorEventDefine.TestLeaderMoveStart, StartMove);
            GameEvent.RemoveEventListener(GameCommon.ActorEventDefine.TestLeaderMoveStop, StopMove);
            GameEvent.RemoveEventListener(GameCommon.ActorEventDefine.TestSetLeader, SetLeader);
            GameEvent.RemoveEventListener<LeaderMoveState>(GameCommon.ActorEventDefine.TestSetLeaderMoveState, SetLeaderMoveState);
        }
    }
}