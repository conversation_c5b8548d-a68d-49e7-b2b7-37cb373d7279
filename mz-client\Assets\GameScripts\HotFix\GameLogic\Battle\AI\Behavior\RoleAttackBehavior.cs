﻿using UnityEngine;

namespace GameLogic
{
    public class RoleAttackBehavior : IAttackBehavior
    {
        public RoleAttackBehavior(RoleController controller)
        {
            Controller = controller;
        }

        private RoleController Controller { get; }

        public void OnAttackStart(Transform target)
        {
            // Debug.Log("开始射击");
            Controller.CurrentTarget = target;
        }

        private const float HeightOffset = 0f;

        public void OnAttackSustain()
        {
            var targetPosition = Controller.CurrentTarget.position + Vector3.up * HeightOffset;
            Controller.FlyWeapon.TryFireAtTargetPosition(targetPosition);
        }

        public void OnAttackEnd()
        {
            // Debug.Log("结束射击");
        }

        public void EjectionAttack(Vector3 targetPos, uint uuid)
        {
            Controller.FlyWeapon.FireProjectileAtTarget(targetPos, uuid);
        }

        public void AttackInDirection(Transform camTrans)
        {
            Controller.FlyWeapon.FireFromCameraDirection(camTrans);
        }
    }
}