using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GameBase;
using GameCommon;
using UnityEngine;

namespace GameLogic
{
    public class BattleWeaponBase : MonoBehaviour
    {

        // 视野范围
        [Header("Attack Settings")]
        [Range(50, 2000)] public float Radious = 50f; // 视野半径
        [Range(1, 360)] public float Angle = 360f; // 视野角度
        [Range(0.1f, 50f)] public float Precision = 0.5f; // 精度
        [Range(0.01f, 1f)] public float LossOfAccuracyPerShot = 1; // 精度损失
        [Range(0, 200)] public int BulletsPerMagazine = 0; // 弹夹储存子弹数量 0为无限
        [Range(0.01f, 30f)] public float ReloadSpeed = 0.5f; // 换弹速度

        // 内置属性
        protected float WeaponRotationSpeed = 20; // 旋转速度
        protected float Fire_Rate = 1f; // 射速
        protected float AimUpOffset = 2; // 瞄准高度偏移量
        protected float CurrentFireRateToShoot; // 当前射速
        protected float Offset = 0.5f; // 子弹发射位置修正
        protected float Life_Time = 5.0f; // 子弹消失时间
        protected bool currentTargetIsVisible; // 当前目标是否可见

        // 瞄准属性
        protected LayerMask PriorSensorLayerMask; // 优先搜索目标层
        protected LayerMask SensorLayerMask; // 搜索目标层
        protected string[] TargetTags = new string[] { "Enemy" };

        // 角色游戏属性
        protected WeaponAttribute Attr;
        protected GameObject Owner; // 所属于的Unit对象
        protected Transform WeaponTransform; // 武器位置
        protected Transform currentTarget; // 当前攻击的目标
        protected bool BeingFillShell = false; // 是否正在填充炮弹
        protected float FillShellTime = 0.0f; // 当前填充时间

        // 武器属性
        protected bool antiaircraft; // 是否制空


        public virtual void Init()
        {
            Attr = Owner.GetComponent<UnitActor>().UnitAttribute.GetCurWeaponsAttr();
            Fire_Rate = Attr.ShortReaction == 0 ? 1.0f : Attr.ShortReaction * 1000f;

            this.antiaircraft = true; //Attr.Antiaircraft == 1;

            if (PriorSensorLayerMask == 0 && this.antiaircraft)
                PriorSensorLayerMask = LayerMask.GetMask("WarPlane");

            if (SensorLayerMask.value == 0)
                SensorLayerMask = LayerMask.GetMask("MainBody", "Armor_Collider");
        }

        // 固定事件扫描目标
        protected virtual void CheckTargets()
        {
            Collider[] targets = GetTargetsInRange(WeaponTransform.position);
            if (targets.Length > 0)
            {
                currentTarget = SelectTargetOnList(targets, TargetTags);
            }
            else
            {
                WeaponTransform.localPosition = Vector3.zero;
                WeaponTransform.rotation = Quaternion.identity;
            }
            Vector3 fieldViewPosition = transform.position + transform.up * (AimUpOffset + 0.2f);

            currentTargetIsVisible = IsVisibleToThisFieldOfView(currentTarget, fieldViewPosition, transform.forward, SensorLayerMask, TagsToConsiderVisible: TargetTags);
        }

        private Collider[] GetTargetsInRange(Vector3 position)
        {
            // 具有制空能力的单位优先搜索WarPlane目标
            if (antiaircraft)
            {
                Collider[] priorityTargets = CheckViewCollider(position, WeaponTransform.forward, PriorSensorLayerMask, this.gameObject);
                if (priorityTargets.Length > 0)
                {
                    return priorityTargets;
                }
            }

            // 没有WarPlane目标时，搜索其他目标（Character, MainBody, Armor_Collider）
            return CheckViewCollider(position, WeaponTransform.forward, SensorLayerMask, this.gameObject);
        }

        // private void ValidateCurrentTarget()
        // {
        //     if (currentTarget == null) return;
        //     if (_cachedDamageCenter == null || _cachedDamageCenter.transform != currentTarget)
        //     {
        //         _cachedDamageCenter = currentTarget.GetComponent<Damage_Control_Center>();
        //     }
        //     if (_cachedDamageCenter != null && _cachedDamageCenter.isDead)
        //     {
        //         currentTarget = null;
        //         _cachedDamageCenter = null;
        //     }
        // }

        // 检查视野内的碰撞体 (Check colliders within field of view)
        public Collider[] CheckViewCollider(Vector3 position, Vector3 forward, LayerMask targetMask, GameObject viewerToIgnore = null)
        {
            List<Collider> colliders = Physics.OverlapSphere(position, Radious, targetMask).ToList();

            foreach (Collider col in colliders)
            {
                Transform target = col.transform;

                Vector3 targetposition = target.position;
                targetposition.y = position.y;

                Vector3 directionToTarget = (targetposition - position).normalized;

                if (Vector3.Angle(forward, directionToTarget) > Angle / 2 || col.gameObject == viewerToIgnore)
                {
                    colliders.Remove(col);
                }
            }

            return colliders.ToArray();
        }

        public Transform SelectTargetOnList(Collider[] targets, string[] allowedTags)
        {
            Transform target = null;
            List<Transform> aliveTargets = new List<Transform>();

            foreach (Collider col in targets)
            {
                if (TagMatches(col.tag, allowedTags))
                {
                    if (col.TryGetComponent(out Damage_Control_Center health))
                    {
                        if (health.Attr.Hp > 0)
                        {
                            aliveTargets.Add(health.transform);
                        }
                        else
                        {
                            aliveTargets.Remove(health.transform);
                        }
                    }
                    else
                    {
                        target = col.transform;
                    }
                }
            }

            if (aliveTargets.Count > 0)
            {
                target = aliveTargets[0];
            }

            return target;
        }

        // bool IsTagAllowed(string tag, string[] allowedTags)
        // {
        //     if (string.IsNullOrEmpty(tag)) return false;
        //     foreach (string allowedTag in allowedTags)
        //     {
        //         if (tag == allowedTag) return true;
        //     }
        //     return false;
        // }

        bool TagMatches(string targetTag, string[] allowedTags)
        {
            if (string.IsNullOrEmpty(targetTag) || allowedTags == null) return false;

            return System.Array.IndexOf(allowedTags, targetTag) >= 0;
        }


        bool IsVisibleToThisFieldOfView(Transform LookedTarget, Vector3 ViewPosition, Vector3 ViewForward, LayerMask LayerMask, float threshold = 0.6f, string[] TagsToConsiderVisible = default(string[]))
        {
            if (LookedTarget == null) return false;

            bool CanSeeTarget = true;
            Vector3 directionToTarget = (LookedTarget.position - ViewPosition).normalized;

            //CAN NOT see the target
            if (Vector3.Angle(ViewForward, directionToTarget) > Angle / 2)
            {
                CanSeeTarget = false;
            }
            else
            {
                float normalDistance = Vector3.Distance(ViewPosition, LookedTarget.position);
                Vector3 lineCastEndPosition = ViewPosition + directionToTarget * normalDistance;

                RaycastHit hit;
                Physics.Linecast(ViewPosition, lineCastEndPosition, out hit, LayerMask);
                if (hit.collider != null)
                {
                    //float distanceToEndPoint = Vector3.Distance(hit.point, lineCastEndPosition);

                    //CAN see the target
                    if (TagMatches(hit.collider.tag, TagsToConsiderVisible) == false)
                    {
                        //Debug.Log(distanceToEndPoint);
                        Debug.DrawLine(hit.point, ViewPosition, Color.cyan);

                        CanSeeTarget = false;
                    }
                    else
                    {
                        CanSeeTarget = true;
                    }
                }
            }

            return CanSeeTarget;
        }


    }
}
