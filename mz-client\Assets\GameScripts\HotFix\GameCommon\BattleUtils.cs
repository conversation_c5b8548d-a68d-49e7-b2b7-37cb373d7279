using System;
using System.Collections.Generic;
using TEngine;
using GameBase;
using UnityEngine;

namespace GameCommon
{
    public class BattleUtils : BehaviourSingleton<BattleUtils>
    {
        // 根据角色ID和角色等级获取战斗角色属性
        public BattleRoleAttribute GetBattleRoleAttribute(int roleId, int roleLevel)
        {
            BattleRoleAttribute roleAttribute = new() { Weapons = new(), Parts = new() };

            var roleInfo = TableMgr.Instance.TableRole.Root[roleId];
            var mechaData = TableMgr.Instance.TableMecha.Root[roleInfo.MechaId];
            roleLevel = Mathf.Max(roleLevel, 1);
            var roleGrow = TableMgr.Instance.TableRoleGrow.Root[roleLevel];

            // 引擎<,,.,
            TableMgr.Instance.TableEngine.Root.TryGetValue(mechaData.EngineID, out var engineData);
            if (engineData != null)
            {
                roleAttribute.MovePower = GetMovePower(engineData.HorsePower, mechaData.MechaWeight, roleLevel);
            }
            else
            {
                roleAttribute.MovePower = GetMovePower(5000, mechaData.MechaWeight, roleLevel);
            }

            // 机甲
            roleAttribute.Hp = mechaData.Hp;
            roleAttribute.Dodge = mechaData.Dodge;
            roleAttribute.ReactionSpeed = mechaData.ReactionSpeed;
            roleAttribute.ReconnaissanceRange = mechaData.ReconnaissanceRange;
            roleAttribute.Visibility = mechaData.Visibility;
            roleAttribute.AttackVisibility = mechaData.AttackVisibility;
            roleAttribute.EnergyConsumption = mechaData.EnergyConsumption;

            // 部件1
            TableMgr.Instance.TablePartHead.Root.TryGetValue(mechaData.MechaHeadID, out var partData1);
            if (partData1 != null)
            {
                roleAttribute.Parts[PartType.Head] = new()
                {
                    Hp = (int)((float)partData1.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    MaxHp = (int)((float)partData1.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    KineticArmor = partData1.KineticArmor,
                    ChemicalArmor = partData1.ChemicalArmor,
                    OrdinaryRicochet = partData1.OrdinaryRicochet,
                    AdvancedRicochet = partData1.AdvancedRicochet,
                    GrenadeRicochet = partData1.GrenadeRicochet,
                };
            }

            // 部件2
            TableMgr.Instance.TablePartBody.Root.TryGetValue(mechaData.MechaBodyID, out var partData2);
            if (partData2 != null)
            {
                roleAttribute.Parts[PartType.Body] = new()
                {
                    Hp = (int)((float)partData2.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    MaxHp = (int)((float)partData2.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    KineticArmor = partData2.KineticArmor,
                    ChemicalArmor = partData2.ChemicalArmor,
                    OrdinaryRicochet = partData2.OrdinaryRicochet,
                    AdvancedRicochet = partData2.AdvancedRicochet,
                    GrenadeRicochet = partData2.GrenadeRicochet,
                };
            }

            // 部件3
            TableMgr.Instance.TablePartLeg.Root.TryGetValue(mechaData.MechaLegID, out var partData3);
            if (partData3 != null)
            {
                roleAttribute.Parts[PartType.Leg] = new()
                {
                    Hp = (int)((float)partData3.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    MaxHp = (int)((float)partData3.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    KineticArmor = partData3.KineticArmor,
                    ChemicalArmor = partData3.ChemicalArmor,
                    OrdinaryRicochet = partData3.OrdinaryRicochet,
                    AdvancedRicochet = partData3.AdvancedRicochet,
                    GrenadeRicochet = partData3.GrenadeRicochet,
                };
            }

            // 部件4
            TableMgr.Instance.TablePartArm.Root.TryGetValue(mechaData.MechaArmID, out var partData4);
            if (partData4 != null)
            {
                roleAttribute.Parts[PartType.Arm] = new()
                {
                    Hp = (int)((float)partData3.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    MaxHp = (int)((float)partData3.HP * ((float)roleGrow.ScaleHp / 10000f)),
                    KineticArmor = partData4.KineticArmor,
                    ChemicalArmor = partData4.ChemicalArmor,
                    OrdinaryRicochet = partData4.OrdinaryRicochet,
                    AdvancedRicochet = partData4.AdvancedRicochet,
                    GrenadeRicochet = partData4.GrenadeRicochet,
                };
            }

            // 武器1
            var weapon1 = TableMgr.Instance.TableWeapon.Root[mechaData.PrimaryWeaponID];
            roleAttribute.Weapons[weapon1.ID] = new WeaponAttribute()
            {
                WeaponType = weapon1.WeaponType,
                Antiaircraft = weapon1.Antiaircraft,
                ReactionSpeed = weapon1.ReactionSpeed / 1000f,
                CurShellCount = weapon1.MagazineLength,
                MagazineLength = weapon1.MagazineLength,
                ShortReaction = weapon1.ShortReaction,
                LongPressShoot = weapon1.LongPressShoot,
                GunNumber = weapon1.GunNumber,
                AmmunitionReserve = weapon1.AmmunitionReserve,
                Shells = new Dictionary<int, ShellAttribute>(),
            };
            foreach (var shellId in weapon1.Shells)
            {
                TableMgr.Instance.TableShell.Root.TryGetValue(shellId, out var shellData);
                if (shellData != null)
                {
                    roleAttribute.Weapons[weapon1.ID].Shells[shellData.ID] = new ShellAttribute()
                    {
                        ShellType = ShellType.AP,
                        PhysicalAttack = (int)((float)shellData.PhysicalAttack * ((float)roleGrow.ScaleDamage / 10000f)),
                        ChemicalAttack = (int)((float)shellData.ChemicalAttack * ((float)roleGrow.ScaleDamage / 10000f)),
                        PiercingDepth = (int)((float)shellData.PiercingDepth * ((float)roleGrow.ScalePiercingDepth / 10000f)),
                        MaximumEntryAngle = shellData.MaximumEntryAngle,
                        CutAngleCorrection = shellData.CutAngleCorrection,
                        StartingSpeed = shellData.StartingSpeed,
                        Weight = shellData.Weight / 1000,
                    };
                }
                else
                {
                    Log.Debug("未找到弹药数据 shellId: {0}", shellId);
                }
            }

            // 武器2
            var weapon2 = TableMgr.Instance.TableWeapon.Root[mechaData.SecondaryWeaponID];
            roleAttribute.Weapons[weapon2.ID] = new WeaponAttribute()
            {
                WeaponType = weapon2.WeaponType,
                Antiaircraft = weapon2.Antiaircraft,
                ReactionSpeed = weapon2.ReactionSpeed / 1000f,
                CurShellCount = weapon2.MagazineLength,
                MagazineLength = weapon2.MagazineLength,
                ShortReaction = weapon2.ShortReaction,
                LongPressShoot = weapon2.LongPressShoot,
                GunNumber = weapon2.GunNumber,
                AmmunitionReserve = weapon2.AmmunitionReserve,
                Shells = new Dictionary<int, ShellAttribute>(),
            };
            foreach (var shellId in weapon2.Shells)
            {
                TableMgr.Instance.TableShell.Root.TryGetValue(shellId, out var shellData);
                if (shellData != null)
                {
                    roleAttribute.Weapons[weapon2.ID].Shells[shellData.ID] = new ShellAttribute()
                    {
                        ShellType = ShellType.AP,
                        PhysicalAttack = (int)((float)shellData.PhysicalAttack * ((float)roleGrow.ScaleDamage / 10000f)),
                        ChemicalAttack = (int)((float)shellData.ChemicalAttack * ((float)roleGrow.ScaleDamage / 10000f)),
                        PiercingDepth = (int)((float)shellData.PiercingDepth * ((float)roleGrow.ScalePiercingDepth / 10000f)),
                        MaximumEntryAngle = shellData.MaximumEntryAngle,
                        CutAngleCorrection = shellData.CutAngleCorrection,
                        StartingSpeed = shellData.StartingSpeed,
                        Weight = shellData.Weight / 1000,
                    };
                }
                else
                {
                    Log.Debug("未找到弹药数据 shellId: {0}", shellId);
                }
            }


            roleAttribute.Hp = (int)((float)roleAttribute.Hp * ((float)roleGrow.ScaleHp / 10000f));
            roleAttribute.MaxHp = roleAttribute.Hp;
            roleAttribute.MovePower = (int)((float)roleAttribute.MovePower * ((float)roleGrow.ScaleMoveSpeed / 10000f));
            roleAttribute.Dodge = (int)((float)roleAttribute.Dodge * ((float)roleGrow.ScaleDodge / 10000f));

            roleAttribute.CurWeaponId = weapon1.ID;
            roleAttribute.Weapons[weapon1.ID].CurShellId = weapon1.Shells[0];

            return roleAttribute;
        }


        /// <summary>
        /// 移动速度
        /// </summary>
        /// <param name="hoursePower"></param>
        /// <param name="weight"></param>
        /// <param name="techLevel"></param>
        /// <returns>每秒移动多少个unity单位</returns>
        public int GetMovePower(int hoursePower, int weight, int techLevel)
        {
            var techFactor = techLevel switch
            {
                1 => 0.1f,
                2 => 0.105f,
                3 => 0.11f,
                _ => 0.1f
            };
            // 马力，重量/t/科技等级
            var movePower = (hoursePower / (float)weight * 8 + 75) * (1.7 - 0.012 * weight);
            // 向上取整
            return (int)Math.Ceiling(movePower * techFactor);
        }
    }
}