﻿using System;
using JUTPS;
using JUTPS.JUInputSystem;
using UnityEngine;
using UnityEngine.InputSystem;

namespace GameLogic
{
    public class ShootTest : MonoBehaviour
    {
        public JUCharacterController characterController;
        public float AimUpOffset = 1;
        public float LookTargetSpeed = 5;
        public Transform currentTarget;
        public BattleWeaponFly flyWeapon;

        private JUInputManager inputManager;
        private InputAction FireAction;
        private Vector3 smoothedTargetPosition;

        private void Start()
        {
            // 获取JU输入管理器
            inputManager = JUInput.Instance();

            inputManager.InputActions.PlayerTest.Shoot.started += OnShootOnClick;
            inputManager.InputActions.PlayerTest.Shoot.canceled += OnShootOnRelease;
            FireAction = inputManager.InputActions.PlayerTest.Shoot;

            // 添加移动输入的回调
            inputManager.InputActions.PlayerTest.Move.started += RoleStartMove;
            inputManager.InputActions.PlayerTest.Move.performed += RoleMoving;
            inputManager.InputActions.PlayerTest.Move.canceled += StopMove;
        }

        private void OnShootOnClick(InputAction.CallbackContext obj)
        {
            Debug.Log("开始射击");
            characterController.FiringMode = true;
            characterController.FiringModeIK = true;
            // characterController.DefaultUseOfAllItems(true, true, false, false, false, true);
        }

        private void OnShootOnRelease(InputAction.CallbackContext obj)
        {
            Debug.Log("射击按键松开");
            characterController.FiringMode = false;
            characterController.FiringModeIK = false;
            // characterController.DefaultUseOfAllItems(false);
        }

        private void Update()
        {
            smoothedTargetPosition = currentTarget != null
                ? Vector3.Lerp(smoothedTargetPosition, currentTarget.position, LookTargetSpeed * Time.deltaTime)
                : smoothedTargetPosition;

            if (FireAction.IsPressed())
            {
                // 按住时每帧执行
                // characterController.LookAtPosition = smoothedTargetPosition + Vector3.up * AimUpOffset;
                Debug.Log("持续射击");
                // characterController.DefaultUseOfAllItems(true, false, false, false, false, true);
                // flyWeapon.TryFireAtTargetPosition(currentTarget.position);
            }
        }

        private void RoleStartMove(InputAction.CallbackContext obj)
        {
            Debug.Log("开始移动");
            characterController.IsMoving = true;
            characterController._Move(0, 0, true);
        }

        private void RoleMoving(InputAction.CallbackContext obj)
        {
            var moveDirection = inputManager.InputActions.PlayerTest.Move.ReadValue<Vector2>();
            Debug.Log($"移动方向: {moveDirection}");
            characterController._Move(moveDirection.x, moveDirection.y, true);
        }

        private void StopMove(InputAction.CallbackContext obj)
        {
            Debug.Log("停止移动");
            characterController.IsMoving = false;
            characterController._Move(0, 0, false);
        }

        private void OnDestroy()
        {
            inputManager.InputActions.PlayerTest.Shoot.started -= OnShootOnClick;
        }
    }
}