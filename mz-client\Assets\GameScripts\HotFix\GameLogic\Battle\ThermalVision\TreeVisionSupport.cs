﻿using UnityEngine;

namespace GameLogic
{
    public class TreeVisionSupport : MonoBehaviour
    {
        private Material _thisMaterial;
        private Color _originalCache = new(0.24f, 0.6f, 0.31f); // 62 152 79

        private void Start()
        {
            _thisMaterial = GetComponent<Renderer>().sharedMaterials[1];
            _originalCache = _thisMaterial.GetColor("_Top_Color");
        }

        public void SetTreeVision(bool isOn)
        {
            if (_thisMaterial == null)
            {
                Debug.Log("RoleVisionSupport: 材质为空");
                return;
            }

            _thisMaterial.SetColor("_Top_Color", isOn ? Color.black : _originalCache);
        }
    }
}