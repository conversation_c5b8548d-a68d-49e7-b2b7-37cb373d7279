﻿using GameBase;
using UnityEngine.UI;
using TEngine;
using UnityEngine;

namespace GameLogic
{
    [Window(UILayer.UI)]
    class UIBattleBulletItem : UIWidget
    {
        #region 脚本工具生成的代码

        private Text m_textShellName;
        private Text m_textShellCount;
        private Image m_imgShellIcon;

        protected override void ScriptGenerator()
        {
            m_textShellName = FindChildComponent<Text>("m_textShellName");
            m_textShellCount = FindChildComponent<Text>("m_textShellCount");
            m_imgShellIcon = FindChildComponent<Image>("m_imgShellIcon");
        }

        #endregion

        #region 事件

        #endregion

        public void SetData(int shellId)
        {
            var shellData = TableMgr.Instance.TableShell.Root[shellId];
            m_textShellName.text = shellData.ShellName;
            Debug.Log($"当前炮弹属性: {shellData.Model}--{shellData.Principle}--{shellData.ShellName}");
        }
    }
}