%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: role_10005_body
  m_Shader: {fileID: 4800000, guid: e4468eb8a8320f7488ddbb0e591f9fbc, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _EMISSIVE_SIMPLE
  - _IS_ANGELRING_OFF
  - _IS_OUTLINE_CLIPPING_NO
  - _IS_TRANSCLIPPING_OFF
  - _OUTLINE_NML
  - _SHADINGGRADEMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    IgnoreProjection: False
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _1st_ShadeMap:
        m_Texture: {fileID: 2800000, guid: 3f8a8905957023e4da0a0861f8d10ceb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _2nd_ShadeMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AngelRing_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AnisotropyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BakedNormal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 727d12483f6f1074b9ac7df101563469, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ClippingMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortionVectorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emissive_Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HighColor_Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 3f8a8905957023e4da0a0861f8d10ceb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCap_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapForMatCap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Outline_Sampler:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_1st_ShadePosition:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_2nd_ShadePosition:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_HighColorMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_MatcapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Set_RimLightMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadingGradeMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmittanceColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _1st2nd_Shades_Feather: 0.886
    - _1st_ShadeColor_Feather: 0.098
    - _1st_ShadeColor_Step: 0.574
    - _2nd_ShadeColor_Feather: 0.886
    - _2nd_ShadeColor_Step: 0
    - _AORemapMax: 1
    - _AORemapMin: 0
    - _ARSampler_AlphaOn: 0
    - _AR_OffsetU: 0
    - _AR_OffsetV: 0.3
    - _ATDistance: 1
    - _AddPrecomputedVelocity: 0
    - _Add_Antipodean_RimLight: 0
    - _AlbedoAffectEmissive: 0
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffPostpass: 0.5
    - _AlphaCutoffPrepass: 0.5
    - _AlphaCutoffShadow: 0.5
    - _AlphaDstBlend: 0
    - _AlphaRemapMax: 1
    - _AlphaRemapMin: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AngelRing: 0
    - _AngelRingOverridden: 0
    - _AngelRingVisible: 1
    - _Anisotropy: 0
    - _Ap_RimLight_FeatherOff: 0
    - _Ap_RimLight_Power: 0.1
    - _AutoRenderQueue: 1
    - _BaseColorOverridden: 0
    - _BaseColorVisible: 1
    - _BaseColor_Step: 0.574
    - _BaseShade_Feather: 0.098
    - _Base_Speed: 0
    - _Blend: 0
    - _BlendMode: 0
    - _BlendModePreserveSpecular: 1
    - _BlurLevelMatcap: 0
    - _BlurLevelSGM: 0
    - _BumpScale: 1
    - _BumpScaleMatcap: 1
    - _CameraRolling_Stabilizer: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ClippingMatteMode: 0
    - _ClippingMode: 0
    - _Clipping_Level: 0.2
    - _CoatMask: 0
    - _ColorShift_Speed: 0
    - _ComposerMaskMode: 0
    - _Cull: 2
    - _CullMode: 0
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DepthOffsetEnable: 0
    - _DetailAlbedoMapScale: 1
    - _DetailAlbedoScale: 1
    - _DetailNormalMapScale: 1
    - _DetailNormalScale: 1
    - _DetailSmoothnessScale: 1
    - _DiffusionProfile: 0
    - _DiffusionProfileHash: 0
    - _DisplacementLockObjectScale: 1
    - _DisplacementLockTilingScale: 1
    - _DisplacementMode: 0
    - _DistortionBlendMode: 0
    - _DistortionBlurBlendMode: 0
    - _DistortionBlurDstBlend: 0
    - _DistortionBlurRemapMax: 1
    - _DistortionBlurRemapMin: 0
    - _DistortionBlurScale: 1
    - _DistortionBlurSrcBlend: 0
    - _DistortionDepthTest: 1
    - _DistortionDstBlend: 0
    - _DistortionEnable: 0
    - _DistortionScale: 1
    - _DistortionSrcBlend: 0
    - _DistortionVectorBias: -1
    - _DistortionVectorScale: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EMISSIVE: 0
    - _EmissiveColorMode: 1
    - _EmissiveExposureWeight: 1
    - _EmissiveIntensity: 1
    - _EmissiveIntensityUnit: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnableGeometricSpecularAA: 0
    - _EnergyConservingSpecularColor: 1
    - _EnvironmentReflections: 1
    - _Farthest_Distance: 100
    - _FirstShadeOverridden: 0
    - _FirstShadeVisible: 1
    - _GI_Intensity: 0
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _HeightAmplitude: 0.02
    - _HeightCenter: 0.5
    - _HeightMapParametrization: 0
    - _HeightMax: 1
    - _HeightMin: -1
    - _HeightOffset: 0
    - _HeightPoMAmplitude: 2
    - _HeightTessAmplitude: 2
    - _HeightTessCenter: 0.5
    - _HighColor_Power: 0
    - _HighlightOverridden: 0
    - _HighlightVisible: 1
    - _InvTilingScale: 1
    - _Inverse_Clipping: 0
    - _Inverse_MatcapMask: 0
    - _Inverse_Z_Axis_BLD: 1
    - _Ior: 1
    - _IridescenceMask: 1
    - _IridescenceThickness: 1
    - _IsBaseMapAlphaAsClippingMask: 0
    - _Is_BLD: 0
    - _Is_BakedNormal: 0
    - _Is_BlendAddToHiColor: 0
    - _Is_BlendAddToMatCap: 1
    - _Is_BlendBaseColor: 0
    - _Is_ColorShift: 0
    - _Is_Filter_HiCutPointLightColor: 1
    - _Is_Filter_LightColor: 0
    - _Is_LightColor_1st_Shade: 1
    - _Is_LightColor_2nd_Shade: 1
    - _Is_LightColor_AR: 1
    - _Is_LightColor_Ap_RimLight: 1
    - _Is_LightColor_Base: 1
    - _Is_LightColor_HighColor: 1
    - _Is_LightColor_MatCap: 1
    - _Is_LightColor_Outline: 1
    - _Is_LightColor_RimLight: 1
    - _Is_NormalMapForMatCap: 0
    - _Is_NormalMapToBase: 0
    - _Is_NormalMapToHighColor: 0
    - _Is_NormalMapToRimLight: 0
    - _Is_Ortho: 0
    - _Is_OutlineTex: 0
    - _Is_PingPong_Base: 0
    - _Is_SpecularToHighColor: 0
    - _Is_UseTweakHighColorOnShadow: 0
    - _Is_UseTweakMatCapOnShadow: 0
    - _Is_ViewCoord_Scroll: 0
    - _Is_ViewShift: 0
    - _LightDirection_MaskOn: 0
    - _LinkDetailsWithBase: 1
    - _MatCap: 0
    - _MaterialID: 1
    - _Metallic: 0
    - _Nearest_Distance: 0.5
    - _NormalMapSpace: 0
    - _NormalScale: 1
    - _OUTLINE: 0
    - _ObjectSpaceUVMapping: 0
    - _OcclusionStrength: 1
    - _Offset_X_Axis_BLD: -0.05
    - _Offset_Y_Axis_BLD: 0.09
    - _Offset_Z: 28.73
    - _OutlineOverridden: 0
    - _OutlineVisible: 1
    - _Outline_Width: 23.61
    - _PPDLodThreshold: 5
    - _PPDMaxSamples: 15
    - _PPDMinSamples: 5
    - _PPDPrimitiveLength: 1
    - _PPDPrimitiveWidth: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RefractionModel: 0
    - _RimLight: 0
    - _RimLightOverridden: 0
    - _RimLightVisible: 1
    - _RimLight_FeatherOff: 1
    - _RimLight_InsideMask: 0.0001
    - _RimLight_Power: 1
    - _Rotate_EmissiveUV: 0
    - _Rotate_MatCapUV: 0
    - _Rotate_NormalMapForMatCapUV: 0
    - _SPRDefaultUnlitColorMask: 15
    - _SRPDefaultUnlitColMode: 1
    - _SSRefractionProjectionModel: 0
    - _Scroll_EmissiveU: 0
    - _Scroll_EmissiveV: 0
    - _SecondShadeOverridden: 0
    - _SecondShadeVisible: 1
    - _Set_SystemShadowsToBase: 1
    - _ShadeColor_Step: 0
    - _Smoothness: 0.5
    - _SmoothnessRemapMax: 1
    - _SmoothnessRemapMin: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularAAScreenSpaceVariance: 0.1
    - _SpecularAAThreshold: 0.2
    - _SpecularHighlights: 1
    - _SpecularOcclusionMode: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _StencilComp: 0
    - _StencilMode: 0
    - _StencilNo: 1
    - _StencilOpFail: 0
    - _StencilOpPass: 0
    - _StencilRef: 2
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 64
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 128
    - _StencilWriteMask: 3
    - _StencilWriteMaskDepth: 32
    - _StencilWriteMaskDistortionVec: 64
    - _StencilWriteMaskGBuffer: 3
    - _StencilWriteMaskMV: 128
    - _StepOffset: 0
    - _SubsurfaceMask: 1
    - _SupportDecals: 1
    - _Surface: 0
    - _SurfaceType: 0
    - _TessEdgeLength: 5
    - _TessExtrusionAmount: 0
    - _TessPhongStrength: 0.5
    - _TessellationBackFaceCullEpsilon: -0.25
    - _TessellationFactor: 4
    - _TessellationFactorMaxDistance: 50
    - _TessellationFactorMinDistance: 20
    - _TessellationFactorTriangleSize: 100
    - _TessellationMode: 0
    - _TessellationShapeFactor: 0.75
    - _TexWorldScale: 1
    - _TexWorldScaleEmissive: 1
    - _Thickness: 1
    - _ThicknessMultiplier: 1
    - _TransmissionEnable: 1
    - _TransmissionMask: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentEnabled: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _TweakHighColorOnShadow: 0
    - _TweakMatCapOnShadow: 0
    - _Tweak_HighColorMaskLevel: -1
    - _Tweak_LightDirection_MaskLevel: 0
    - _Tweak_MatCapUV: 0
    - _Tweak_MatcapMaskLevel: 0
    - _Tweak_RimLightMaskLevel: -0.36
    - _Tweak_ShadingGradeMapLevel: 0
    - _Tweak_SystemShadowsLevel: 0.065
    - _Tweak_transparency: -0.26
    - _UVBase: 0
    - _UVDetail: 0
    - _UVEmissive: 0
    - _Unlit_Intensity: 1
    - _UseEmissiveIntensity: 0
    - _UseShadowThreshold: 0
    - _Use_1stAs2nd: 0
    - _Use_BaseAs1st: 0
    - _WorkflowMode: 1
    - _ZOverDrawMode: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestModeDistortion: 8
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteMode: 1
    - _isUnityToonshader: 1
    - _simpleUI: 0
    - _utsTechnique: 1
    - _utsVersionX: 0
    - _utsVersionY: 11
    - _utsVersionZ: 0
    m_Colors:
    - _1st_ShadeColor: {r: 0.5698112, g: 0.5698112, b: 0.5698112, a: 1}
    - _2nd_ShadeColor: {r: 1, g: 1, b: 1, a: 1}
    - _AngelRingMaskColor: {r: 0, g: 1, b: 0, a: 1}
    - _AngelRing_Color: {r: 1, g: 1, b: 1, a: 1}
    - _Ap_RimLightColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
    - _BaseColorMaskColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorShift: {r: 0, g: 0, b: 0, a: 1}
    - _DiffusionProfileAsset: {r: 0, g: 0, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColorLDR: {r: 0, g: 0, b: 0, a: 1}
    - _Emissive_Color: {r: 0, g: 0, b: 0, a: 1}
    - _FirstShadeMaskColor: {r: 0, g: 1, b: 1, a: 1}
    - _HighColor: {r: 1, g: 1, b: 1, a: 1}
    - _HighlightMaskColor: {r: 1, g: 1, b: 0, a: 1}
    - _InvPrimScale: {r: 1, g: 1, b: 0, a: 0}
    - _IridescenceThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineMaskColor: {r: 0, g: 0, b: 0, a: 1}
    - _Outline_Color: {r: 0.21698111, g: 0.21698111, b: 0.21698111, a: 1}
    - _RimLightColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimLightMaskColor: {r: 1, g: 0, b: 1, a: 1}
    - _SecondShadeMaskColor: {r: 0, g: 0, b: 1, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _ThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _TransmittanceColor: {r: 1, g: 1, b: 1, a: 1}
    - _UVDetailsMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskEmissive: {r: 1, g: 0, b: 0, a: 0}
    - _ViewShift: {r: 0, g: 0, b: 0, a: 1}
    - emissive: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &3169897903348942967
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
