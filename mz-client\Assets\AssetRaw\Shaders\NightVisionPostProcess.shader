﻿Shader "Custom/NightVisionEnhanced"
{
    Properties
    {
        _MainTex("Main Texture", 2D) = "white" {}
        _BrightnessGain("整体亮度增益", Range(0.1,2)) = 1.5
        _GreenFactor("绿色滤镜强度", Range(0, 1)) = 0.8
        _ExposureThreshold("亮部高光阈值", Range(0.5, 1)) = 0.8
        _DarkThreshold("暗部阈值", Range(0, 0.5)) = 0.2
        _DarkBoostAmount("暗部提亮系数", Range(1, 3)) = 1.5
    }

    SubShader
    {
        Tags
        {
            "RenderType"="Opaque"
        }
        Pass
        {
            ZTest Always Cull Off ZWrite Off
            CGPROGRAM
            #pragma vertex vert_img
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;

            float _BrightnessGain;
            float _GreenFactor;
            float _ExposureThreshold;
            float _DarkThreshold;
            float _DarkBoostAmount;

            fixed4 frag(v2f_img i) : SV_Target
            {
                float2 uv = i.uv;
                float3 color = tex2D(_MainTex, uv).rgb;

                // Gamma校正，提亮暗部细节
                color = pow(color, 0.5);

                // 计算灰度
                float gray = dot(color, float3(0.3, 0.59, 0.11));

                // 暗部增强处理
                float enhancedGray;
                if (gray < _DarkThreshold)
                {
                    // 极暗部分保持原样或稍暗，避免死黑
                    enhancedGray = gray * 0.8;
                }
                else
                {
                    // 阈值以上暗部线性提亮
                    float t = saturate((gray - _DarkThreshold) / (1.0 - _DarkThreshold));
                    enhancedGray = lerp(_DarkThreshold, gray * _DarkBoostAmount, t);
                }

                // 亮度增益叠加偏移，保证视觉亮度层次
                float lifted = saturate((enhancedGray + 0.05) * _BrightnessGain);

                // 夜视绿色调，lerp控制绿调强度
                float3 greenTint = float3(lifted * 0.15, lifted, lifted * 0.15);
                float3 nightColor = lerp(float3(lifted, lifted, lifted), greenTint, _GreenFactor);

                // 高光处理（模拟刺眼光源）
                if (gray > _ExposureThreshold)
                {
                    float glow = saturate((gray - _ExposureThreshold) * 10.0);
                    nightColor += glow * float3(1, 1, 1);
                }

                return fixed4(saturate(nightColor), 1.0);
            }
            ENDCG
        }
    }
}