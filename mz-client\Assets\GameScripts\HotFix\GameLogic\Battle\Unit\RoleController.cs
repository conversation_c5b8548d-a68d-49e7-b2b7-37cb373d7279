﻿using System.Collections;
using JUTPS.CoverSystem;
using UnityEngine;
using UnityEngine.AI;

namespace GameLogic
{
    public class RoleController : BaseController
    {
        private RoleMoveComponent MoveComponent { get; set; } // 移动组件
        private Transform _firePoint;

        public Transform CurrentTarget;
        public TempBattleFlyWeapon FlyWeapon; // 武器系统脚本

        public override void Initialize(UnitActor unitActor)
        {
            this.UnitActor = unitActor;
            BodyTransform = unitActor.MainBody;

            var coverController = unitActor.GetComponent<JUCoverController>();

            MoveComponent = new RoleMoveComponent(unitActor.CharacterController, UnitActor, coverController);

            _firePoint = BodyTransform;

            // ToDo: 获取武器系统脚本 -- 暂时只获取一侧的武器
            FlyWeapon = unitActor.transform.Find("WeaponPoint2").GetComponentInChildren<TempBattleFlyWeapon>();
            FlyWeapon.Init();
            AttackBehavior = new RoleAttackBehavior(this);
        }

        public override void MoveToTarget(Vector3 targetPosition)
        {
            // 检查单位是否处于停泊挡位
            if (UnitActor.Blackboard.TryGetValue(BlackboardKeys.CurrSpeedGear, out var currentGear) &&
                currentGear == GearLevel.Stop)
            {
                // 确保停止移动
                MoveComponent.Stop();
                return;
            }

            // 正常移动逻辑
            MoveComponent.MoveTo(targetPosition);
        }

        public override void PerformAttack()
        {
            Debug.Log("开始攻击");
        }

        #region 移动相关

        public override void StopMovement()
        {
            MoveComponent.Stop();
        }

        public override void Stop()
        {
            MoveComponent.Stop();
            UnitActor.Blackboard.Set(BlackboardKeys.MoveTargetPosition, BattleConstants.EmptyTargetPoint);
        }

        // 保持面朝前方方向向后移动
        public void MoveBack(float distance = 2f)
        {
            // 计算后退目标点（向后distance米）
            Vector3 backward = -BodyTransform.forward;
            Vector3 retreatTarget = BodyTransform.position + backward * distance;

            // 设置目标点
            MoveComponent.MoveTo(retreatTarget);
        }

        // 如果需要，在这里恢复面向控制
        public void ResetRotationControl()
        {
            Debug.Log("恢复面向控制");
        }

        public override bool GetMoveStatu()
        {
            return MoveComponent.IsMoving;
        }

        public override void SetMoveSpeed(float speed)
        {
            if (speed == 0 && MoveComponent.IsRunning)
            {
                MoveComponent.IsRunning = false;
            }
            else
            {
                MoveComponent.IsRunning = true;
            }

            MoveComponent.SetMoveSpeed(speed);
        }

        #endregion


        // 检查射线是否被友军阻挡
        private bool IsFireLineBlocked(Vector3 from, Vector3 to)
        {
            Vector3 direction = to - from;
            float distance = direction.magnitude;

            // 创建一个不包含自身的射线检测层
            int layerMask = Physics.DefaultRaycastLayers;
            layerMask &= ~(1 << BodyTransform.gameObject.layer); // 排除自身层

            // 射线检测
            if (Physics.Raycast(from, direction.normalized, out RaycastHit hit, distance, layerMask))
            {
                // 检查是否击中友军
                var hitUnit = hit.collider.GetComponentInParent<UnitActor>();
                if (hitUnit != null && hitUnit.Camp == UnitActor.Camp)
                {
                    // 击中友军，射线被阻挡
                    Debug.DrawLine(from, hit.point, Color.magenta, 0.5f);
                    Debug.Log("射线被友军阻挡");
                    return true;
                }

                // 击中敌人或环境，射线未被友军阻挡
                Debug.DrawLine(from, hit.point, Color.cyan, 0.5f);
                return false;
            }

            // 射线未击中任何物体，未被阻挡
            Debug.DrawLine(from, to, Color.blue, 0.5f);
            return false;
        }

        // 尝试找到一个清晰的射击位置
        private bool TryFindClearShot(Vector3 targetPos, out Vector3 adjustedFirePos)
        {
            // 默认值
            adjustedFirePos = _firePoint.position;

            // 尝试在水平面上偏移几个位置
            Vector3[] offsets =
            {
                new(1f, 0, 0),
                new(-1f, 0, 0),
                new(0, 0, 1f),
                new(0, 0, -1f),
                new(1f, 0, 1f),
                new(-1f, 0, 1f),
                new(1f, 0, -1f),
                new(-1f, 0, -1f)
            };

            foreach (var offset in offsets)
            {
                Vector3 testPos = _firePoint.position + offset;
                if (!IsFireLineBlocked(testPos, targetPos))
                {
                    adjustedFirePos = testPos;
                    return true;
                }
            }

            // 尝试垂直偏移
            Vector3 elevatedPos = _firePoint.position + Vector3.up * 0.5f;
            if (!IsFireLineBlocked(elevatedPos, targetPos))
            {
                adjustedFirePos = elevatedPos;
                return true;
            }

            // 无法找到清晰射线
            return false;
        }

        // 请求重新定位
        private void RequestRepositioning()
        {
            // 通知AI系统需要重新定位
            UnitActor.Blackboard.Set(BlackboardKeys.NeedsRepositioning, true);
        }

        // 检查射线是否被友军阻挡（供感知系统使用）
        public bool CheckFireLineBlocked(Vector3 from, Vector3 to)
        {
            Vector3 direction = to - from;
            float distance = direction.magnitude;

            // 创建一个不包含自身的射线检测层
            int layerMask = Physics.DefaultRaycastLayers;
            layerMask &= ~(1 << BodyTransform.gameObject.layer); // 排除自身层

            // 射线检测
            if (Physics.Raycast(from, direction.normalized, out RaycastHit hit, distance, layerMask))
            {
                // 检查是否击中友军
                var hitUnit = hit.collider.GetComponentInParent<UnitActor>();
                if (hitUnit != null && hitUnit.Camp == UnitActor.Camp)
                {
                    // 击中友军，射线被阻挡
                    Debug.DrawLine(from, hit.point, Color.magenta, 0.5f);
                    return true;
                }
            }

            return false; // 射线未被友军阻挡
        }

        // 移动到更好的位置（供感知系统使用）
        public void MoveToOptimalPosition(Vector3 targetPosition, Vector3 avoidDirection)
        {
            // 计算到目标的方向
            Vector3 directionToTarget = (targetPosition - BodyTransform.position).normalized;

            // 计算垂直于目标方向和避开方向的向量
            Vector3 sideDirection = Vector3.Cross(directionToTarget, Vector3.up).normalized;

            // 如果侧向与避开方向相似，选择相反方向
            if (Vector3.Dot(sideDirection, avoidDirection) > 0)
            {
                sideDirection = -sideDirection;
            }

            // 计算新位置
            Vector3 newPosition = BodyTransform.position + sideDirection * 3f;

            // 确保新位置在导航网格上
            NavMeshHit hit;
            if (NavMesh.SamplePosition(newPosition, out hit, 3f, NavMesh.AllAreas))
            {
                MoveToTarget(hit.position);
            }
            else
            {
                // 如果找不到有效的导航点，尝试向后移动
                MoveBack(2f);
            }
        }

        public override float GetMoveSpeed()
        {
            return UnitActor.CharacterController.RunSpeed;
        }

        public override bool HasReachedDestination()
        {
            return !MoveComponent.IsMoving;
        }

        public override void RestoreMove()
        {
            MoveComponent.RestoreMove();
        }

        public override void SetRunningState(bool runningState)
        {
            MoveComponent.IsRunning = runningState;
        }
    }
}