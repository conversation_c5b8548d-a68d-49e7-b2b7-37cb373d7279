﻿Shader "Unlit/ThermalHeatSourceShader"
{
    Properties
    {
        _MainTex("Base Map", 2D) = "white" {}
        _Temperature("Temperature", Float) = 1.0
    }
    SubShader
    {
        Tags
        {
            "RenderType"="Opaque"
            "Queue"="Geometry+1"
        }
        Cull Back

        // Pass 0: 只写深度，不写颜色
        Pass
        {
            Name "DepthOnly"
            ZWrite On
            ZTest LEqual
            ColorMask 0

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragDepth
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"


            struct appdata
            {
                float4 vertex : POSITION;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
            };

            v2f vert(appdata v)
            {
                v2f o;
                o.pos = TransformObjectToHClip(v.vertex);
                return o;
            }

            // 深度写入时不输出颜色
            float4 fragDepth(v2f i) : SV_Target
            {
                // 不输出颜色，仅写深度
                return float4(0, 0, 0, 0);
            }
            ENDHLSL
        }

        // Pass 1: 写颜色，开启深度测试但关闭深度写入，半透明混合
        Pass
        {
            Name "ColorPass"
            ZWrite Off
            ZTest LEqual
            Blend SrcAlpha OneMinusSrcAlpha

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragColor
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"

            sampler2D _MainTex;
            float _Temperature;

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
            };

            v2f vert(appdata v)
            {
                v2f o;
                o.pos = TransformObjectToHClip(v.vertex);
                o.uv = v.uv;
                return o;
            }

            float4 _CameraDepthTexture_TexelSize;

            float LinearEyeDepth(float rawDepth)
            {
                return 1.0 / (_ZBufferParams.z * rawDepth + _ZBufferParams.w);
            }


            float4 fragColor(v2f i) : SV_Target
            {
                float2 screenUV = i.pos.xy / i.pos.w * 0.5 + 0.5;

                float rawDepth = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_CameraDepthTexture, screenUV);
                float sceneLinearDepth = LinearEyeDepth(rawDepth);

                float fragLinearDepth = i.pos.z / i.pos.w;

                if (fragLinearDepth > sceneLinearDepth + 0.001)
                    discard;

                float3 baseColor = tex2D(_MainTex, i.uv).rgb;
                float luminance = dot(baseColor, float3(0.299, 0.587, 0.114));
                float heat = luminance * _Temperature;

                return float4(heat, heat, heat, 1);
            }
            ENDHLSL
        }
    }
}