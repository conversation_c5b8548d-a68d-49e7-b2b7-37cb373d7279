using System.Collections;
using GameCommon;
using TEngine;
using UnityEngine;

namespace GameLogic
{
    public class BattleWeaponFly : BattleWeaponBase
    {
        public GameObject AP_Bullet_Prefab; // 子弹

        void Awake()
        {
            Owner = this.transform.parent.parent.gameObject;
            WeaponTransform = this.transform;
        }
        public override void Init()
        {
            base.Init();
        }

        void Start()
        {
            this.Init();
            InvokeRepeating(nameof(CheckTargets), 0.5f, 0.5f);
        }

        void Update()
        {
            if (this.BeingFillShell)
            {
                if (this.Attr.AmmunitionReserve > 0)
                {
                    // Debug.Log("备弹充足，正在填充弹药");
                    this.FillShellTime += Time.deltaTime;
                    // Debug.Log($"填充弹药时间：{this.FillShellTime}/{this.Attr.ReactionSpeed}");
                    if (this.FillShellTime >= this.Attr.ReactionSpeed)
                    {
                        // Debug.Log("填充弹药完成");
                        CurrentFireRateToShoot = Fire_Rate; // 填充完弹药可以直接射击
                        this.CurrentFireRateToShoot = 0; // 重置为0而不是Fire_Rate
                        this.FillShellTime = 0f; // 完全重置装填时间
                        this.BeingFillShell = false;
                        int fillCount = Mathf.Min(this.Attr.MagazineLength, this.Attr.AmmunitionReserve); // 更安全的计算方式
                        this.Attr.CurShellCount = fillCount;
                        this.Attr.AmmunitionReserve -= fillCount;
                    }
                }
                // else
                // {
                //     Debug.Log("备弹不足，停止填充弹药");
                // }
                return;
            }

            // 自动射击
            AutoShoot();
        }


        protected IEnumerator Generate_Bullet()
        {
            // 质量
            var mass = this.Attr.GetCurShellAttr().Weight;
            // 速度
            float velocity = this.Attr.GetCurShellAttr().StartingSpeed;

            Vector3 targetVelocity = Vector3.zero;
            Rigidbody targetRb = currentTarget.GetComponent<Rigidbody>();
            if (targetRb != null)
                targetVelocity = targetRb.velocity; // 坦克
            else
                targetVelocity = currentTarget.transform.forward * BattleDefined.AirStrikePlaneSpeed; // 飞机 (修正：移除Time.deltaTime)

            Vector3 predictedPosition = currentTarget.transform.position;

            if (targetVelocity.sqrMagnitude > 0.1f) // 只在目标移动时预测
            {
                float distance = Vector3.Distance(transform.position, predictedPosition);
                float timeToTarget = distance / velocity;
                // 使用基准位置而非累积位置，避免误差叠加
                predictedPosition = currentTarget.transform.position + targetVelocity * timeToTarget;
            }

            // 计算方向
            Vector3 direction = (predictedPosition - transform.position).normalized;

            // 创建子弹并设置方向
            GameObject bulletObject = Instantiate(
                AP_Bullet_Prefab,
                this.transform.position + (direction * Offset),
                Quaternion.LookRotation(direction)
            );

            Battle_Shell_Control bulletScript = bulletObject.GetComponent<Battle_Shell_Control>();
            bulletScript.SetOwner(this.gameObject);
            bulletScript.Life_Time = Life_Time;
            bulletScript.Debug_Flag = true;

            // Set the tag.
            bulletObject.tag = "Finish"; // (Note.) The ray cast for aiming does not hit any object with "Finish" tag.

            // Set the layer.
            bulletObject.layer = BattleColliderLayer.Bullet;

            // Shoot.
            yield return new WaitForFixedUpdate();

            // 设置子弹速度
            Rigidbody rigidbody = bulletObject.GetComponent<Rigidbody>();
            Vector3 currentVelocity = direction * velocity;
            rigidbody.velocity = currentVelocity;
            rigidbody.mass = mass;
        }


        #region 新增函数

        void AutoShoot()
        {
            if (!currentTarget || this.BeingFillShell)
                return;

            var direction = Quaternion.LookRotation(currentTarget.transform.position - this.transform.position);
            this.transform.rotation = direction;

            if (this.currentTargetIsVisible)
            {
                CurrentFireRateToShoot += Time.deltaTime;
                // Debug.Log($"射击频率 :  {CurrentFireRateToShoot}/{Fire_Rate}");
                if (CurrentFireRateToShoot >= Fire_Rate)
                {
                    if (this.Attr.CurShellCount > 0) // 添加弹药检查
                    {
                    CurrentFireRateToShoot = 0; // 重置为0而不是减去Fire_Rate
                    StartCoroutine(nameof(Generate_Bullet));
                    this.Attr.CurShellCount--;
                    // Debug.Log("当前弹药数量 " + this.Attr.CurShellCount);

                    if (this.Attr.CurShellCount <= 0)
                    {
                        // Debug.Log("准备自动换弹");
                        this.BeingFillShell = true;
                        this.FillShellTime = 0.0f;
                        GameEvent.Send(ActorEventDefine.ReactionSpeedEvent, this.Attr.ReactionSpeed);
                    }
                    }
                    else
                    {
                        CurrentFireRateToShoot = Fire_Rate; // 防止连续检查
                    }
                }
            }
        }

        /// <summary>
        /// 向指定目标点抛射（受射速限制）
        /// </summary>
        /// <param name="targetPosition"></param>
        /// <param name="launchAngle"></param>
        /// <param name="shellUuid"></param>
        public void FireProjectileAtTarget(Vector3 targetPosition, uint shellUuid, float launchAngle = 45f)
        {
            StartCoroutine(Generate_Projectile_Trajectory(targetPosition, shellUuid, launchAngle));
        }

        private IEnumerator Generate_Projectile_Trajectory(Vector3 targetPos, uint shellUuid, float launchAngleDeg)
        {
            GameObject bulletObject = Instantiate(AP_Bullet_Prefab, this.transform.position, Quaternion.identity);

            Battle_Shell_Control bulletScript = bulletObject.GetComponent<Battle_Shell_Control>();
            bulletScript.isEjectionMode = true;
            bulletScript.shellUuid = shellUuid;
            bulletScript.Initial_Velocity = this.Attr.GetCurShellAttr().StartingSpeed;
            var dcc = Owner.GetComponent<Damage_Control_Center>();
            bulletScript.Attr = dcc.Attr.GetCurWeaponsAttr().GetCurShellAttr();
            bulletScript.Life_Time = Life_Time;
            bulletScript.SetOwner(this.Owner);

            bulletObject.tag = "Finish";
            bulletObject.layer = BattleColliderLayer.Bullet;

            yield return new WaitForFixedUpdate();

            var rigidbody = bulletObject.GetComponent<Rigidbody>();

            // 获取抛射速度向量
            Vector3 velocity;
            bool valid = TryCalculateBallisticVelocity(targetPos, transform.position, launchAngleDeg, out velocity);
            if (!valid)
            {
                Debug.LogWarning("抛射计算失败，目标过远或过近");
                Destroy(bulletObject);
                yield break;
            }

            // 设置朝向
            transform.rotation = Quaternion.LookRotation(-velocity.normalized);

            // 设置速度
            rigidbody.velocity = velocity;
            bulletScript.isFlying = true;
        }

        /// <summary>
        /// 根据起点、终点和角度计算抛射初速度（忽略空气阻力）
        /// </summary>
        private bool TryCalculateBallisticVelocity(Vector3 target, Vector3 origin, float angleDeg, out Vector3 velocity)
        {
            velocity = Vector3.zero;

            var gravity = Mathf.Abs(Physics.gravity.y);
            var angleRad = angleDeg * Mathf.Deg2Rad;

            var planarTarget = new Vector3(target.x, 0f, target.z);
            var planarOrigin = new Vector3(origin.x, 0f, origin.z);

            var distance = Vector3.Distance(planarTarget, planarOrigin);
            var yOffset = target.y - origin.y;

            var cosAngle = Mathf.Cos(angleRad);
            var sinAngle = Mathf.Sin(angleRad);

            var denominator = 2f * (cosAngle * cosAngle) * (distance * Mathf.Tan(angleRad) - yOffset);

            if (denominator <= 0.001f)
                return false; // 无解或目标太近

            var speedSqr = (gravity * distance * distance) / denominator;

            if (speedSqr <= 0f)
                return false;

            var speed = Mathf.Sqrt(speedSqr);

            // 方向
            var direction = (planarTarget - planarOrigin).normalized;

            velocity = direction * speed * cosAngle + Vector3.up * speed * sinAngle;
            return true;
        }

        #endregion
    }
}