using System.Collections.Generic;

namespace GameLogic
{
    public enum CommandType
    {
        AIControl = 0, //ai自动
        Empty, //空指令

        MoveTo, // 移动
        Stop, // 停止移动
        ShootTarget, // 射击目标

        // 行为指令
        Charge, //冲锋
        Forward, //前进
        Retreat, //撤退
        FollowMe, //跟随
        QuicklyDisperse, //快速分散
        FindCover, //找掩体
        MoveBack, //后退
        Standing, //站立

        // 攻击指令
        SelectAttackTarget, // 选择攻击目标
        Bump, // 冲撞
        Ejection, // 抛射
        FixBayonets, // 上刺刀
        AutoFire, // 自动射击
        AutoSkill, // 自动技能
        UseSkill, // 使用技能
        UseEquipment, // 使用装备

        // 状态指令
        PostureStanding, // 立姿
        PostureCrouching, // 跪姿
        PosturePronePosition, // 卧姿
        PostureAtEase, // 稍息
        PostureChange, // 姿态切换
        GearStop, // 停泊
        GearLow, // 低速
        GearNormal, // 巡航
        GearFast, // 极速
        GearChange, // 速度档位切换

        // 阵型指令
        FormationEmpty, //空阵
        FormationDefault, //默认
        FormationShieldWall, //盾墙
        FormationLine, //线列阵
        FormationRing, //环
        FormationHourglass, //漏斗阵
        FormationWedge, //楔形阵
        FormationTwoByTwo, //两列两排
        FormationDisturb, //打乱阵型
    }

    public enum FormationType
    {
        Empty, //空阵
        Default,
        ShieldWall, //盾墙
        Line, //线列阵
        Ring, //环
        Hourglass, //漏斗阵
        Wedge, //楔形阵
        TwoByTwo, //两列两排
        Disturb, //打乱阵型
    }

    public class BattleDefined
    {
        /// <summary>
        /// 单位标签
        /// </summary>
        public static List<string> UnitTags = new() { "Player", "Enemy", "Tank", "Armor_Collider" };

        public static float AirStrikePlaneSpeed = 100f; // 空袭飞机移动速度
    }
}