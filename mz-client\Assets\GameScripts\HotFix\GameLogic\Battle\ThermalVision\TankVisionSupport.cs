﻿using System;
using UnityEngine;

namespace GameLogic
{
    public class TankVisionSupport : MonoBehaviour
    {
        private Material _thisMaterial;

        private void Start()
        {
            _thisMaterial = GetComponent<Renderer>().sharedMaterial;
        }

        public void SetSpecialVision(bool isOn)
        {
            if (_thisMaterial == null)
            {
                Debug.Log("RoleVisionSupport: 材质为空");
                return;
            }

            // 开启发射
            if (isOn)
            {
                _thisMaterial.EnableKeyword("_EMISSION");
            }
            else
            {
                _thisMaterial.DisableKeyword("_EMISSION");
            }
        }
    }
}