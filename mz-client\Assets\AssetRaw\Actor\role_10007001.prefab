%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &40508369612838380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 447577384055631140}
  m_Layer: 9
  m_Name: Bone011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &447577384055631140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 40508369612838380}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000070223955, y: -0.000000019210344, z: -0.26386338, w: 0.9645601}
  m_LocalPosition: {x: -0.72650266, y: 0, z: 1.4551915e-13}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4356446074273187628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &108622669542037327
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7565358681497497537}
  m_Layer: 9
  m_Name: Bone015
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7565358681497497537
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108622669542037327}
  serializedVersion: 2
  m_LocalRotation: {x: 0.8175631, y: 0.5574943, z: 0.14312428, w: -0.017498288}
  m_LocalPosition: {x: -0.6979567, y: 1.7545956, z: -0.28925002}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9082533802662949271}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &138359012802089565
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2332294850227083599}
  m_Layer: 9
  m_Name: Bone009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2332294850227083599
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 138359012802089565}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6058448, y: 0.36462626, z: 0.6058446, w: 0.3646259}
  m_LocalPosition: {x: -0.0002531191, y: 1.8396072, z: -0.80375195}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4356446074273187628}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &218078450588685720
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2842749508136079699}
  m_Layer: 9
  m_Name: mixamorig:RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2842749508136079699
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 218078450588685720}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00016257576, y: 0.022969028, z: 0.9996354, w: -0.014194856}
  m_LocalPosition: {x: 0.32129496, y: -0.15547302, z: -0.0067596994}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1886683036069179337}
  m_Father: {fileID: 2159168449819813484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &248082557169629353
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1526434676040984410}
  - component: {fileID: 8096624828021629626}
  m_Layer: 0
  m_Name: P226 PISTOLA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1526434676040984410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 248082557169629353}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4304369206372039083}
  - {fileID: 4723263975245869706}
  - {fileID: 132038375780274545}
  - {fileID: 7176533583860097523}
  m_Father: {fileID: 1706510617622443713}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8096624828021629626
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 248082557169629353}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9c29b532223494aa4ebe4c1f1f8b9bf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Radious: 50
  Angle: 360
  Precision: 0.5
  LossOfAccuracyPerShot: 1
  BulletsPerMagazine: 10
  ReloadSpeed: 0.5
  AP_Bullet_Prefab: {fileID: 1459669234869594, guid: 2b4848108857e264f832cb19d952938d,
    type: 3}
--- !u!1 &423133319151880789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4304369206372039083}
  - component: {fileID: 2946424566645495395}
  - component: {fileID: 1045529743476942233}
  m_Layer: 0
  m_Name: P226_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4304369206372039083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 423133319151880789}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1526434676040984410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2946424566645495395
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 423133319151880789}
  m_Mesh: {fileID: 7596302699937668379, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &1045529743476942233
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 423133319151880789}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &486579977911323315
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1326752086651692242}
  - component: {fileID: 5678378746580861274}
  m_Layer: 9
  m_Name: Small Gun Prevent Cliping
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1326752086651692242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 486579977911323315}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5678378746580861274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 486579977911323315}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &531707115493505031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2844537960111159810}
  m_Layer: 9
  m_Name: L_eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2844537960111159810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 531707115493505031}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006705523, y: 9.094945e-13, z: 3.1459638e-13, w: 1}
  m_LocalPosition: {x: -0.39195332, y: 0.6157135, z: 0.7258972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &594482432521134261
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4510859382338563736}
  - component: {fileID: 7327842607408352430}
  - component: {fileID: 2935453731664599754}
  m_Layer: 9
  m_Name: meimao
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4510859382338563736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 594482432521134261}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 5.1704283, z: -0.24068451}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7327842607408352430
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 594482432521134261}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9945c61ec10ebb2499413d583a1de55e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 8958222113155434507, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 4229804192053380875}
  - {fileID: 3814118144412981443}
  - {fileID: 5320926853093469176}
  - {fileID: 2063291850693473760}
  - {fileID: 3554942883285928025}
  - {fileID: 7928309776927184478}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7928309776927184478}
  m_AABB:
    m_Center: {x: 0.26677302, y: 0.025593398, z: 0.035251595}
    m_Extent: {x: 0.63561237, y: 0.036495358, z: 0.07246098}
  m_DirtyAABB: 0
--- !u!114 &2935453731664599754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 594482432521134261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &675553596467827604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2059085919011269367}
  m_Layer: 9
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2059085919011269367
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 675553596467827604}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000054331754, y: 0.0000053700523, z: 0.70573246, w: 0.70847845}
  m_LocalPosition: {x: 3.1991158, y: 6.459683, z: -0.124483354}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &704464234986411133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 350698881400451608}
  m_Layer: 9
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &350698881400451608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 704464234986411133}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000038817602, y: -0.000000042132214, z: 0.09174421, w: 0.9957826}
  m_LocalPosition: {x: -0.5261078, y: -0.00000061035155, z: 0.000000009536743}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8301692803115576651}
  m_Father: {fileID: 338711804419288855}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &812351411493225793
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3814118144412981443}
  m_Layer: 9
  m_Name: Dummy014
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3814118144412981443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 812351411493225793}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000074651226, y: 0.000000005666288, z: 0.123189494, w: 0.9923832}
  m_LocalPosition: {x: 0.4356784, y: 0.9260074, z: 0.6018632}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &979015028426648433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5666053432917101740}
  m_Layer: 9
  m_Name: Bone007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5666053432917101740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 979015028426648433}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023786767, y: 0.000000015654566, z: -0.06567001, w: 0.9978414}
  m_LocalPosition: {x: -0.63039154, y: -0.0000018310546, z: 0.000000057220458}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4344206908818970472}
  m_Father: {fileID: 7528594999894934019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1037169130785425959
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2489279427381488538}
  - component: {fileID: 7670236032469405206}
  - component: {fileID: 4623928614761173166}
  m_Layer: 9
  m_Name: Weapon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2489279427381488538
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1037169130785425959}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0.8312526, y: 3.492397, z: 0.220322}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7670236032469405206
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1037169130785425959}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2afe1fa799571074abb67aab6d4788dd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -3714880803914648012, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 2059085919011269367}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2059085919011269367}
  m_AABB:
    m_Center: {x: -2.9670792, y: -0.07301402, z: 0.000574138}
    m_Extent: {x: 3.9097137, y: 0.63922554, z: 0.12508848}
  m_DirtyAABB: 0
--- !u!114 &4623928614761173166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1037169130785425959}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1199296198520840210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3323014790432019716}
  m_Layer: 9
  m_Name: Dummy008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3323014790432019716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1199296198520840210}
  serializedVersion: 2
  m_LocalRotation: {x: 0.99599534, y: -0.08940543, z: 0.000000012103846, w: -0.00000013260696}
  m_LocalPosition: {x: -0.4347992, y: 0.74099, z: 0.5722082}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1241995310619170095
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6721003154060706106}
  m_Layer: 9
  m_Name: mixamorig:RightHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6721003154060706106
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1241995310619170095}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.00057762145, y: 0.03895523, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8811829750699825161}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1310782851913020142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4443393235076110565}
  m_Layer: 9
  m_Name: Dummy007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4443393235076110565
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1310782851913020142}
  serializedVersion: 2
  m_LocalRotation: {x: 0.99963695, y: 0.02694543, z: -0.000000002791569, w: -0.00000013313118}
  m_LocalPosition: {x: -0.61023754, y: 0.7458069, z: 0.4843225}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1336825961805314639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9130350769055994120}
  m_Layer: 9
  m_Name: mixamorig:LeftHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9130350769055994120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1336825961805314639}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.013396835, y: 0.038960494, z: 0.00000061035155}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6376274829410319525}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1492208653928578982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4723263975245869706}
  - component: {fileID: 4609929827944503959}
  - component: {fileID: 2886442616543017581}
  m_Layer: 0
  m_Name: P226_Gatilho
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4723263975245869706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492208653928578982}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.00003252851, y: -0.0030121086, z: -0.033351615}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1526434676040984410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4609929827944503959
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492208653928578982}
  m_Mesh: {fileID: -6085457374283068898, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &2886442616543017581
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492208653928578982}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1575001819048801099
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8771635536770140149}
  m_Layer: 9
  m_Name: Dummy004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8771635536770140149
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1575001819048801099}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006318977, y: 0.00000001641987, z: 0.25149795, w: 0.96785784}
  m_LocalPosition: {x: 0.5722262, y: 0.5049524, z: 0.4843226}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1581645225489517659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1886683036069179337}
  m_Layer: 9
  m_Name: mixamorig:RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1886683036069179337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1581645225489517659}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0768952, y: 0.002420304, z: -0.029351646, w: 0.99660414}
  m_LocalPosition: {x: -0, y: 1.2209827, z: -0.000000047683713}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6799646646942304111}
  m_Father: {fileID: 2842749508136079699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1631679384255672590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7928309776927184478}
  m_Layer: 9
  m_Name: Dummy018
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7928309776927184478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1631679384255672590}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -3.2370309e-12, z: -2.1490147e-12, w: 0.7071067}
  m_LocalPosition: {x: -0.26673913, y: 0.8466833, z: 0.64301634}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1705556243127188652
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4049274269355597357}
  m_Layer: 9
  m_Name: mixamorig:LeftHandMiddle4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4049274269355597357
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1705556243127188652}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.001207695, y: 0.046716, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8791853670228756238}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1709549018077734259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2039811504258756515}
  - component: {fileID: 2573084667211634791}
  m_Layer: 9
  m_Name: Flash Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2039811504258756515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1709549018077734259}
  serializedVersion: 2
  m_LocalRotation: {x: -0.47304577, y: -0.4621461, z: -0.47734848, w: 0.5786079}
  m_LocalPosition: {x: 0.302, y: 0.167, z: 0.258}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2573084667211634791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1709549018077734259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &1799512956139495374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5996799717741359484}
  m_Layer: 9
  m_Name: mixamorig:Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5996799717741359484
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1799512956139495374}
  serializedVersion: 2
  m_LocalRotation: {x: -1.3877788e-17, y: 1e-45, z: -5.04871e-29, w: 1}
  m_LocalPosition: {x: -0, y: 0.38595185, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8627290112246882791}
  - {fileID: 882835569133387009}
  - {fileID: 3751691097141516430}
  m_Father: {fileID: 5081096013438664983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1844117527497148049
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 647024267804474169}
  m_Layer: 9
  m_Name: mixamorig:LeftHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &647024267804474169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1844117527497148049}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000035658488, y: 0.00045240205, z: 0.0035268073, w: 0.9999937}
  m_LocalPosition: {x: -0.0008686447, y: 0.0734462, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8791853670228756238}
  m_Father: {fileID: 2646999674787043067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1901407478026870962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1706510617622443713}
  m_Layer: 9
  m_Name: WeaponPoint2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1706510617622443713
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901407478026870962}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2, y: 7, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1526434676040984410}
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2119460721624462213
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2528228882701661677}
  m_Layer: 9
  m_Name: mixamorig:RightHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2528228882701661677
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2119460721624462213}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21736924, y: 0.040166385, z: 0.22089954, w: 0.9499162}
  m_LocalPosition: {x: -0.06993183, y: 0.13312805, z: 0.06977508}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2721921557697649038}
  m_Father: {fileID: 5735808257124294363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2300394480886723416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4349125886652700671}
  m_Layer: 9
  m_Name: Bone012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4349125886652700671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2300394480886723416}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6151142, y: 0.77457166, z: 0.082366124, w: -0.122020826}
  m_LocalPosition: {x: 0.6101535, y: 1.8016866, z: -0.51051396}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9144868439852211942}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2452022395409875026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6729750463399930231}
  m_Layer: 9
  m_Name: Dummy003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6729750463399930231
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2452022395409875026}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000074525424, y: 0.000000013457309, z: 0.24923933, w: 0.9684419}
  m_LocalPosition: {x: 0.22912645, y: 0.7007419, z: 0.5722079}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2575561630944766336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6799646646942304111}
  m_Layer: 9
  m_Name: mixamorig:RightFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6799646646942304111
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2575561630944766336}
  serializedVersion: 2
  m_LocalRotation: {x: 0.43444303, y: 0.026112603, z: -0.015191324, w: 0.90019256}
  m_LocalPosition: {x: 0.00000015258789, y: 1.1092385, z: -0.000000057220458}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9103391460933089211}
  m_Father: {fileID: 1886683036069179337}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2606468763635789476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5043173571052745744}
  - component: {fileID: 6888526942961643288}
  m_Layer: 9
  m_Name: Big Gun Prevent Clipping
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5043173571052745744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2606468763635789476}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6888526942961643288
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2606468763635789476}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &2632586966889744382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6644516247294459313}
  - component: {fileID: 4808091398009818296}
  - component: {fileID: 2136015010986766821}
  m_Layer: 0
  m_Name: P226_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6644516247294459313
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2632586966889744382}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7248280167301000214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4808091398009818296
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2632586966889744382}
  m_Mesh: {fileID: 7596302699937668379, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &2136015010986766821
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2632586966889744382}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2880444433358634035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7176533583860097523}
  - component: {fileID: 7212043010550275197}
  - component: {fileID: 1644055401103137661}
  m_Layer: 0
  m_Name: p226_Slide
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7176533583860097523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2880444433358634035}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.0003915865, y: 0.044852935, z: -0.03657893}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1526434676040984410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7212043010550275197
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2880444433358634035}
  m_Mesh: {fileID: -8272425351879887783, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &1644055401103137661
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2880444433358634035}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2914595452259995071
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9082533802662949271}
  m_Layer: 9
  m_Name: Bone016
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9082533802662949271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2914595452259995071}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0021694226, y: -0.029018085, z: -0.056728534, w: 0.9979655}
  m_LocalPosition: {x: -0.621842, y: 0, z: 0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7881030511182108868}
  m_Father: {fileID: 7565358681497497537}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2944244754454815407
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7262944662503666424}
  m_Layer: 9
  m_Name: mixamorig:RightHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7262944662503666424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2944244754454815407}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010766218, y: -0.0035702682, z: 0.005222422, w: 0.99992204}
  m_LocalPosition: {x: -0.00675766, y: 0.40725327, z: -0.00880371}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1021241047454088735}
  m_Father: {fileID: 5735808257124294363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3019956688694756999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7881030511182108868}
  m_Layer: 9
  m_Name: Bone017
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7881030511182108868
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3019956688694756999}
  serializedVersion: 2
  m_LocalRotation: {x: 0.019531772, y: -0.07397972, z: -0.097440846, w: 0.99229574}
  m_LocalPosition: {x: -0.6424893, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9082533802662949271}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3057633219241295767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2721921557697649038}
  m_Layer: 9
  m_Name: mixamorig:RightHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2721921557697649038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3057633219241295767}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00013216867, y: -0.0017020908, z: -0.009622038, w: 0.99995226}
  m_LocalPosition: {x: -0.008973617, y: 0.13231765, z: 0.00000030517577}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5612105991538806793}
  m_Father: {fileID: 2528228882701661677}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3100660830676995348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5081096013438664983}
  m_Layer: 9
  m_Name: mixamorig:Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5081096013438664983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3100660830676995348}
  serializedVersion: 2
  m_LocalRotation: {x: -1.3877788e-17, y: -2.147656e-13, z: 6.4883136e-13, w: 1}
  m_LocalPosition: {x: -0, y: 0.3377069, z: -0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5996799717741359484}
  m_Father: {fileID: 1845932228718614015}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3116620993059728173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2646999674787043067}
  m_Layer: 9
  m_Name: mixamorig:LeftHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2646999674787043067
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3116620993059728173}
  serializedVersion: 2
  m_LocalRotation: {x: -0.020690769, y: -0.002255145, z: -0.019537063, w: 0.9995925}
  m_LocalPosition: {x: 0.011120147, y: 0.4064273, z: -0.016820678}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 647024267804474169}
  m_Father: {fileID: 869308139326682320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3203039704215082128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464340304123392555}
  m_Layer: 9
  m_Name: mixamorig:LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &464340304123392555
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3203039704215082128}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14134644, y: 0.00008810772, z: -0.037269454, w: 0.9892584}
  m_LocalPosition: {x: -0.0000003814697, y: 0.3765917, z: 0.00000061035155}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3529489055650455530}
  m_Father: {fileID: 8627290112246882791}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3304252277391154924
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8484602595043628219}
  m_Layer: 9
  m_Name: mixamorig:RightForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8484602595043628219
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3304252277391154924}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006598733, y: -0.00066398917, z: 0.00000011610073, w: 0.9999998}
  m_LocalPosition: {x: 0.00000030517577, y: 0.5155226, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5735808257124294363}
  m_Father: {fileID: 390046516716207638}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3350662207319133244
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4344151855918140763}
  m_Layer: 9
  m_Name: mixamorig:LeftHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4344151855918140763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3350662207319133244}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000069073695, y: 0.00029563266, z: -0.00437575, w: 0.9999904}
  m_LocalPosition: {x: 0.0005527687, y: 0.071366884, z: 0.0000012207031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3473154600333279016}
  m_Father: {fileID: 4925751383943211775}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3375584162555590433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6412882093267405258}
  m_Layer: 9
  m_Name: Dummy009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6412882093267405258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3375584162555590433}
  serializedVersion: 2
  m_LocalRotation: {x: 0.9684419, y: -0.24923939, z: 0.000000034698683, w: -0.00000012856711}
  m_LocalPosition: {x: -0.2806553, y: 0.7007419, z: 0.5722079}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3466741099855365299
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8572670108132032713}
  m_Layer: 9
  m_Name: mixamorig:LeftHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8572670108132032713
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3466741099855365299}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00014235443, y: 0.0021300327, z: 0.02251484, w: 0.99974424}
  m_LocalPosition: {x: 0.005854645, y: 0.13271835, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6376274829410319525}
  m_Father: {fileID: 1162548375716080965}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3483140071838632847
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2063291850693473760}
  m_Layer: 9
  m_Name: Dummy016
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2063291850693473760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3483140071838632847}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -3.2370309e-12, z: -2.1490147e-12, w: 0.7071067}
  m_LocalPosition: {x: -0.5546733, y: 0.94296235, z: 0.6018632}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3484850432669396432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2026669618834362001}
  m_Layer: 9
  m_Name: Dummy012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2026669618834362001
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3484850432669396432}
  serializedVersion: 2
  m_LocalRotation: {x: 0.9531311, y: 0.30255768, z: -0.00000003388676, w: -0.00000012894905}
  m_LocalPosition: {x: -0.30399805, y: 0.46381682, z: 0.4843223}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3555665282001168408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6248195400013430237}
  - component: {fileID: 6501306577644775956}
  m_Layer: 9
  m_Name: Small Weapon Position Reference
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6248195400013430237
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3555665282001168408}
  serializedVersion: 2
  m_LocalRotation: {x: -0.1272034, y: 0.029375885, z: -0.65810245, w: 0.7415238}
  m_LocalPosition: {x: 0.212, y: 0.227, z: 0.407}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6501306577644775956
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3555665282001168408}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &3561039359102804599
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 561305600587650171}
  m_Layer: 9
  m_Name: mixamorig:Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &561305600587650171
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3561039359102804599}
  serializedVersion: 2
  m_LocalRotation: {x: 9.195651e-36, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.11758706e-10, y: 0.1196112, z: 0.029433707}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 338711804419288855}
  - {fileID: 7528594999894934019}
  - {fileID: 2332294850227083599}
  - {fileID: 4349125886652700671}
  - {fileID: 7565358681497497537}
  - {fileID: 7511126210637667772}
  - {fileID: 4683307285138841727}
  - {fileID: 6283733638883419277}
  - {fileID: 6729750463399930231}
  - {fileID: 8771635536770140149}
  - {fileID: 4405265322804230557}
  - {fileID: 1917396652922928935}
  - {fileID: 4443393235076110565}
  - {fileID: 3323014790432019716}
  - {fileID: 6412882093267405258}
  - {fileID: 8640813325590577738}
  - {fileID: 4871670533839514625}
  - {fileID: 2026669618834362001}
  - {fileID: 4229804192053380875}
  - {fileID: 3814118144412981443}
  - {fileID: 5320926853093469176}
  - {fileID: 2063291850693473760}
  - {fileID: 3554942883285928025}
  - {fileID: 7928309776927184478}
  - {fileID: 2844537960111159810}
  - {fileID: 3356139804833992853}
  - {fileID: 3566285818775239524}
  m_Father: {fileID: 882835569133387009}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3836294066380907943
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8553622461001826082}
  m_Layer: 9
  m_Name: mixamorig:LeftFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8553622461001826082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3836294066380907943}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49907574, y: -0.019216185, z: 0.014392588, w: 0.8662257}
  m_LocalPosition: {x: 0.000000019073486, y: 1.1160978, z: -0.000000019073486}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1126074499856568765}
  m_Father: {fileID: 3824296566610331598}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3928924023943342960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 132038375780274545}
  - component: {fileID: 118383012443770967}
  - component: {fileID: 6132604577307767124}
  m_Layer: 0
  m_Name: P226_Magazine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &132038375780274545
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3928924023943342960}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0004212472, y: -0.03791171, z: 0.0173402}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1526434676040984410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &118383012443770967
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3928924023943342960}
  m_Mesh: {fileID: 5997363134491779355, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &6132604577307767124
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3928924023943342960}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4001398685522180610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4356446074273187628}
  m_Layer: 9
  m_Name: Bone010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4356446074273187628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4001398685522180610}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000008818529, y: -0.00000003592885, z: -0.020697169, w: 0.9997858}
  m_LocalPosition: {x: -0.7309027, y: 0.00000030517577, z: 0.000000025834888}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 447577384055631140}
  m_Father: {fileID: 2332294850227083599}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4106964461684672828
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 338711804419288855}
  m_Layer: 9
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &338711804419288855
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4106964461684672828}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000002581035, y: -0.0000000050148445, z: 0.57699025, w: 0.81675106}
  m_LocalPosition: {x: -0.8264269, y: 1.6691464, z: 0.23286977}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 350698881400451608}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4141535052758109232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4145682087461902105}
  m_Layer: 9
  m_Name: mixamorig:RightToe_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4145682087461902105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4141535052758109232}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.17134954, z: -0.0000000047683715}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9103391460933089211}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4186819226589089433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7443811283614267304}
  m_Layer: 9
  m_Name: mixamorig:LeftHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7443811283614267304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4186819226589089433}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00019786613, y: 0.008582621, z: -0.064943865, w: 0.997852}
  m_LocalPosition: {x: 0.005236015, y: 0.05931076, z: 0.0000012207031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2643127265135017745}
  m_Father: {fileID: 4871632405394784032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4380317014226329036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5320926853093469176}
  m_Layer: 9
  m_Name: Dummy015
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5320926853093469176
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380317014226329036}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006305484, y: 0.000000011169613, z: 0.17442572, w: 0.98467034}
  m_LocalPosition: {x: 0.29922932, y: 0.874559, z: 0.64301664}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4423511201992922348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2927468740787406367}
  m_Layer: 9
  m_Name: mixamorig:RightHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2927468740787406367
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4423511201992922348}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010838994, y: 0.00876113, z: 0.012764276, w: 0.9998214}
  m_LocalPosition: {x: 0.11044479, y: 0.339086, z: 0.0096551515}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 139379554654945687}
  m_Father: {fileID: 5735808257124294363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4451443529909036286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1162548375716080965}
  m_Layer: 9
  m_Name: mixamorig:LeftHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1162548375716080965
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4451443529909036286}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21534361, y: -0.045588605, z: -0.2297602, w: 0.94802904}
  m_LocalPosition: {x: 0.07151628, y: 0.13347565, z: 0.06815369}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8572670108132032713}
  m_Father: {fileID: 869308139326682320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4499104895686125906
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7941417664471192683}
  m_Layer: 9
  m_Name: mixamorig:LeftToe_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7941417664471192683
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4499104895686125906}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000038146972, y: 0.17179655, z: -0.000000011920928}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1126074499856568765}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4500652354776209268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9144868439852211942}
  m_Layer: 9
  m_Name: Bone013
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9144868439852211942
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4500652354776209268}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023915027, y: -0.000000050404644, z: 0.023779629, w: 0.99971724}
  m_LocalPosition: {x: -0.572464, y: -0.00000030517577, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 779431428164314511}
  m_Father: {fileID: 4349125886652700671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4630364463844625911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7190343714480834740}
  - component: {fileID: 8131419413555073926}
  m_Layer: 9
  m_Name: Item Wield Rotation Center
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7190343714480834740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4630364463844625911}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7125183737757033826}
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8131419413555073926
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4630364463844625911}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 46c4e480d339e3445b0747684b45e81a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  WeaponPositionsLengh: 6
  WeaponPositionName:
  - Small Weapon Position Reference
  - Big Weapon Position Reference
  - Flash Light
  - Left Hand Small Weapon Position
  - Small Gun Prevent Cliping
  - Big Gun Prevent Clipping
  WeaponPositionTransform:
  - {fileID: 6248195400013430237}
  - {fileID: 8228817392633141322}
  - {fileID: 2039811504258756515}
  - {fileID: 7886711489498952697}
  - {fileID: 1326752086651692242}
  - {fileID: 5043173571052745744}
  ID: 000000000100000002000000030000000400000005000000
  _storedLocalPositions:
  - {x: 0.212, y: 0.227, z: 0.407}
  - {x: 0.207, y: 0.14, z: 0.24}
  - {x: 0.302, y: 0.167, z: 0.258}
  - {x: 0.217, y: 0.046, z: 0.259}
  - {x: 0, y: 0, z: 0}
  - {x: 0, y: 0, z: 0}
  _storedLocalRotations:
  - {x: -0.1272034, y: 0.029375885, z: -0.65810245, w: 0.7415238}
  - {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
  - {x: -0.47304577, y: -0.4621461, z: -0.47734848, w: 0.5786079}
  - {x: -0.33424655, y: -0.5801304, z: -0.65039605, w: 0.35876584}
  - {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
  - {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
--- !u!1 &4701507691702589825
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4229804192053380875}
  m_Layer: 9
  m_Name: Dummy013
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4229804192053380875
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4701507691702589825}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006705523, y: 9.094945e-13, z: 3.1459638e-13, w: 1}
  m_LocalPosition: {x: 0.5871636, y: 0.9564456, z: 0.6018632}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4725268969935288301
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7511126210637667772}
  m_Layer: 9
  m_Name: Bone021
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7511126210637667772
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4725268969935288301}
  serializedVersion: 2
  m_LocalRotation: {x: -0.108363, y: -0.15523842, z: 0.78591186, w: 0.58864343}
  m_LocalPosition: {x: 0.6459056, y: 1.6892916, z: 1.1710664}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1435930432157879822}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4750311453272527256
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4871632405394784032}
  m_Layer: 9
  m_Name: mixamorig:LeftHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4871632405394784032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4750311453272527256}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00006975363, y: -0.0030561474, z: 0.06517342, w: 0.9978693}
  m_LocalPosition: {x: -0.0035359191, y: 0.0833696, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7443811283614267304}
  m_Father: {fileID: 996235153334202700}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4869917442989510056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1845932228718614015}
  m_Layer: 9
  m_Name: mixamorig:Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1845932228718614015
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4869917442989510056}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12825614, y: -0.000043272157, z: 0.00026801086, w: 0.99174106}
  m_LocalPosition: {x: -2.9802322e-10, y: 0.27994078, z: -0.07364116}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5081096013438664983}
  m_Father: {fileID: 2159168449819813484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4952115390558623486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1126074499856568765}
  m_Layer: 9
  m_Name: mixamorig:LeftToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1126074499856568765
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4952115390558623486}
  serializedVersion: 2
  m_LocalRotation: {x: 0.31945023, y: 0.01650869, z: -0.0055662403, w: 0.9474429}
  m_LocalPosition: {x: 0.000000095367426, y: 0.41461673, z: 0.000000104904174}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7941417664471192683}
  m_Father: {fileID: 8553622461001826082}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5100594285825362983
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3473154600333279016}
  m_Layer: 9
  m_Name: mixamorig:LeftHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3473154600333279016
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5100594285825362983}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000060619323, y: 0.0009827632, z: -0.005348206, w: 0.9999852}
  m_LocalPosition: {x: -0.00006912231, y: 0.06877685, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6518118568121173389}
  m_Father: {fileID: 4344151855918140763}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5140601790786489522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1435930432157879822}
  m_Layer: 9
  m_Name: Bone022
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1435930432157879822
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5140601790786489522}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000038881156, y: -0.000000010733298, z: -0.14167061,
    w: 0.9899139}
  m_LocalPosition: {x: -0.552247, y: 0.00000015258789, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4418418818962142617}
  m_Father: {fileID: 7511126210637667772}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5323627263031967879
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 779431428164314511}
  m_Layer: 9
  m_Name: Bone014
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &779431428164314511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5323627263031967879}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000023132023, y: -2.6477034e-10, z: 0.11371804, w: 0.9935131}
  m_LocalPosition: {x: -0.7275912, y: 0.00000030517577, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9144868439852211942}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5374334991744096957
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7125183737757033826}
  m_Layer: 9
  m_Name: Item Wielding Hands Positions
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7125183737757033826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5374334991744096957}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6248195400013430237}
  - {fileID: 8228817392633141322}
  - {fileID: 2039811504258756515}
  - {fileID: 7886711489498952697}
  - {fileID: 1326752086651692242}
  - {fileID: 5043173571052745744}
  m_Father: {fileID: 7190343714480834740}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5465745908471830567
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8811829750699825161}
  m_Layer: 9
  m_Name: mixamorig:RightHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8811829750699825161
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5465745908471830567}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000031888447, y: -0.00019236862, z: -0.013868011, w: 0.99990386}
  m_LocalPosition: {x: 0.0009954452, y: 0.07712478, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6721003154060706106}
  m_Father: {fileID: 1021241047454088735}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5618269524637660713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4418418818962142617}
  m_Layer: 9
  m_Name: Bone023
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4418418818962142617
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5618269524637660713}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0039459025, y: 0.014079408, z: -0.16922303, w: 0.98546934}
  m_LocalPosition: {x: -0.5286658, y: 0, z: 0.000000038146972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1435930432157879822}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5637949062056253117
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3554942883285928025}
  m_Layer: 9
  m_Name: Dummy017
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3554942883285928025
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5637949062056253117}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -3.2370309e-12, z: -2.1490147e-12, w: 0.7071067}
  m_LocalPosition: {x: -0.4031883, y: 0.90652436, z: 0.6018632}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5761613293196534652
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2812878794726008757}
  m_Layer: 9
  m_Name: mixamorig:RightHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2812878794726008757
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5761613293196534652}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010820522, y: 0.00041843168, z: -0.00884384, w: 0.9999023}
  m_LocalPosition: {x: -0.103686996, y: 0.40190595, z: 0.010488891}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7332885288040405272}
  m_Father: {fileID: 5735808257124294363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5816055117491582851
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2898793082032515967}
  m_Layer: 9
  m_Name: WeaponPoint1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2898793082032515967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5816055117491582851}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2, y: 7, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7248280167301000214}
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5826755417502389441
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9078839242738391539}
  - component: {fileID: 3638758896468532093}
  - component: {fileID: 9086679242019640145}
  m_Layer: 0
  m_Name: P226_Gatilho
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9078839242738391539
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5826755417502389441}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.00003252851, y: -0.0030121086, z: -0.033351615}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7248280167301000214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3638758896468532093
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5826755417502389441}
  m_Mesh: {fileID: -6085457374283068898, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &9086679242019640145
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5826755417502389441}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5870107582537015710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7940277349427510050}
  - component: {fileID: 7591044176967728145}
  - component: {fileID: 6406339176553475676}
  m_Layer: 0
  m_Name: P226_Magazine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7940277349427510050
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5870107582537015710}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0004212472, y: -0.03791171, z: 0.0173402}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7248280167301000214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7591044176967728145
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5870107582537015710}
  m_Mesh: {fileID: 5997363134491779355, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &6406339176553475676
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5870107582537015710}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6067229997579887445
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 390046516716207638}
  m_Layer: 9
  m_Name: mixamorig:RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &390046516716207638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6067229997579887445}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14063813, y: -0.0016343833, z: 0.039859336, w: 0.98925704}
  m_LocalPosition: {x: 0.000000076293944, y: 0.37659174, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8484602595043628219}
  m_Father: {fileID: 3751691097141516430}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6119594437979435966
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7886711489498952697}
  - component: {fileID: 6059194100519123025}
  m_Layer: 9
  m_Name: Left Hand Small Weapon Position
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7886711489498952697
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119594437979435966}
  serializedVersion: 2
  m_LocalRotation: {x: -0.33424655, y: -0.5801304, z: -0.65039605, w: 0.35876584}
  m_LocalPosition: {x: 0.217, y: 0.046, z: 0.259}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6059194100519123025
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6119594437979435966}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &6125476153525373676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1294561579366372785}
  m_Layer: 9
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1294561579366372785
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6125476153525373676}
  serializedVersion: 2
  m_LocalRotation: {x: 2.6666342e-15, y: 7.483174e-15, z: 0.12102902, w: 0.99264896}
  m_LocalPosition: {x: -0.6533307, y: 0.0000005340576, z: 0.000000038146972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8301692803115576651}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6200496689054707690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3751691097141516430}
  m_Layer: 9
  m_Name: mixamorig:RightShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3751691097141516430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6200496689054707690}
  serializedVersion: 2
  m_LocalRotation: {x: -0.54328775, y: -0.44137505, z: 0.58684677, w: -0.40698576}
  m_LocalPosition: {x: 0.17305231, y: 0.38620788, z: -0.013075179}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 390046516716207638}
  m_Father: {fileID: 5996799717741359484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6359765520005630054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6155586233555599963}
  m_Layer: 9
  m_Name: mixamorig:RightHandThumb4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6155586233555599963
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359765520005630054}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.016535264, y: 0.043712005, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5612105991538806793}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6424670116828577561
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2159168449819813484}
  m_Layer: 9
  m_Name: mixamorig:Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2159168449819813484
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6424670116828577561}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000005994429, y: 0.000008538907, z: -0.00027134738, w: 1}
  m_LocalPosition: {x: 0.000102454695, y: 2.7339938, z: -0.0069315103}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8478144065346117392}
  - {fileID: 2842749508136079699}
  - {fileID: 1845932228718614015}
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6552009734885710914
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5883595087999649528}
  - component: {fileID: 524271727762349186}
  - component: {fileID: 4952900802513728152}
  - component: {fileID: 5101679399052707993}
  - component: {fileID: 1695094724556425457}
  - component: {fileID: 7299334403609959943}
  - component: {fileID: 5307526407119770209}
  - component: {fileID: 1307181696719956790}
  - component: {fileID: 6357630325359916132}
  - component: {fileID: 8508650648846955072}
  - component: {fileID: 4352694967239775742}
  - component: {fileID: 708415186624860627}
  - component: {fileID: 7181221774905008198}
  - component: {fileID: 4406486220611190624}
  - component: {fileID: 6501581849669830767}
  - component: {fileID: 6431247798047166587}
  - component: {fileID: 4907944412144306944}
  - component: {fileID: 1999539453065640018}
  - component: {fileID: 1596389044794768680}
  - component: {fileID: 935697178488449131}
  m_Layer: 9
  m_Name: role_10007001
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5883595087999649528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 81.71, y: 0, z: 113.70242}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2898793082032515967}
  - {fileID: 1706510617622443713}
  - {fileID: 8068520012858600233}
  - {fileID: 2059085919011269367}
  - {fileID: 2860550690573192381}
  - {fileID: 9063282056373606929}
  - {fileID: 4510859382338563736}
  - {fileID: 2159168449819813484}
  - {fileID: 3016230864625929017}
  - {fileID: 2489279427381488538}
  - {fileID: 7190343714480834740}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &524271727762349186
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Controller: {fileID: 9100000, guid: 8ab68869583251843b7c3cf781ec2c83, type: 2}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!136 &4952900802513728152
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Material: {fileID: 13400000, guid: 4f035ee96ae055847b4d1853741ad44b, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.4
  m_Height: 1.7
  m_Direction: 1
  m_Center: {x: 0, y: 0.85, z: 0}
--- !u!114 &5101679399052707993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 488f7c24132cdf1418b0255398c8c3d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  CapsuleToResize: {fileID: 0}
  HeightOffset: 0.73
  ProneAndRollCenterY: 0.38
  CenterOffset: {x: 0, y: 0, z: 0}
--- !u!54 &1695094724556425457
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  serializedVersion: 4
  m_Mass: 85
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 112
  m_CollisionDetection: 3
--- !u!114 &7299334403609959943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: be2f1b4d5da6234438ceca64fb2aa3f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpDirection: {x: 0, y: 0, z: 0}
  UpOrientation: {x: 0, y: 0, z: 0, w: 0}
  anim: {fileID: 0}
  rb: {fileID: 0}
  coll: {fileID: 0}
  MyPivotCamera: {fileID: 0}
  CharacterHealth: {fileID: 6431247798047166587}
  DriveVehicleAbility: {fileID: 0}
  Inventory: {fileID: 0}
  LeftHandDamager: {fileID: 0}
  RightHandDamager: {fileID: 0}
  LeftFootDamager: {fileID: 0}
  RightFootDamager: {fileID: 0}
  VelocityMultiplier: 0
  DirectionTransform: {fileID: 0}
  DesiredDirection: {x: 0, y: 0, z: 0}
  LocomotionMode: 0
  SetRigidbodyVelocity: 1
  FireModeMaxTime: 1
  Speed: 8
  WalkSpeed: 0.5
  CrouchSpeed: 0.4
  RunSpeed: 1.2
  SprintingSpeedMax: 3
  SprintingAcceleration: 2
  SprintingDeceleration: 0.6
  RotationSpeed: 3
  JumpForce: 3
  StoppingSpeed: 2
  AirInfluenceControll: 0.5
  MaxWalkableAngle: 45
  CurvedMovement: 1
  LerpRotation: 1
  BodyInclination: 1
  MovementAffectsWeaponAccuracy: 0
  OnMovePrecision: 4
  LookAtPosition: {x: 0, y: 0, z: 0}
  SprintingSkill: 0
  SprintOnRunButton: 0
  UnlimitedSprintDuration: 0
  GroundAngleDesaceleration: 1
  GroundAngleDesacelerationMultiplier: 1.5
  GroundAngle: 0
  GroundNormal: {x: 0, y: 0, z: 0}
  GroundPoint: {x: 0, y: 0, z: 0}
  RootMotion: 0
  RootMotionSpeed: 1
  RootMotionRotation: 0
  RootMotionDeltaPosition: {x: 0, y: 0, z: 0}
  RagdollWhenDie: 0
  Events:
    OnDeath:
      m_PersistentCalls:
        m_Calls: []
    OnRessurect:
      m_PersistentCalls:
        m_Calls: []
    OnRun:
      m_PersistentCalls:
        m_Calls: []
    OnSprinting:
      m_PersistentCalls:
        m_Calls: []
    OnRoll:
      m_PersistentCalls:
        m_Calls: []
    OnJump:
      m_PersistentCalls:
        m_Calls: []
    OnCrouch:
      m_PersistentCalls:
        m_Calls: []
    OnGetUp:
      m_PersistentCalls:
        m_Calls: []
    OnStartMoving:
      m_PersistentCalls:
        m_Calls: []
    OnIdle:
      m_PersistentCalls:
        m_Calls: []
    OnEnterFireMode:
      m_PersistentCalls:
        m_Calls: []
    OnExitFireMode:
      m_PersistentCalls:
        m_Calls: []
    OnPunch:
      m_PersistentCalls:
        m_Calls: []
  WhatIsGround:
    serializedVersion: 2
    m_Bits: 71681
  GroundCheckRadius: 0.1
  GroundCheckHeighOfsset: 0.1
  GroundCheckSize: 0.5
  WhatIsWall:
    serializedVersion: 2
    m_Bits: 0
  WallRayHeight: 1
  WallRayDistance: 0.6
  EnableStepCorrection: 1
  UpStepSpeed: 5
  StepCorrectionMask:
    serializedVersion: 2
    m_Bits: 71681
  FootstepHeight: 0.4
  ForwardStepOffset: 0.6
  StepHeight: 0.02
  EnableUngroundedStepUp: 1
  UngroundedStepUpSpeed: 4
  UngroundedStepUpRayDistance: 0.1
  StoppingTimeOnStepPosition: 0.5
  AnimatorParameters:
    _BaseLayerIndex: 0
    _LegsLayerIndex: 1
    _RightArmLayerIndex: 2
    _LeftArmLayerIndex: 3
    _BothArmsLayerIndex: 4
    _SwitchWeaponLayerIndex: 5
    Moving: Moving
    Running: Running
    Speed: Speed
    HorizontalInput: Horizontal
    VerticalInput: Vertical
    IdleTurn: IdleTurn
    MovingTurn: MovingTurn
    Grounded: Grounded
    Jumping: Jumping
    ItemEquiped: ItemEquiped
    FireMode: FireMode
    Crouch: Crouched
    Prone: Prone
    Driving: Driving
    Dying: Die
    Punch: Punch
    Roll: Roll
    ReloadRightWeapon: ReloadRightWeapon
    ReloadLeftWeapon: ReloadLeftWeapon
    PullWeaponSlider: PullWeaponSlider
    LandingIntensity: LandingIntensity
    ItemWieldingRightHandPoseID: ItemWieldingRightHandPoseID
    ItemWieldingLeftHandPoseID: ItemWieldingLeftHandPoseID
    ItemsWieldingIdentifier: ItemsWieldingIdentifier
  PivotItemRotation: {fileID: 4630364463844625911}
  WeaponHoldingPositions: {fileID: 0}
  HoldableItemInUseRightHand: {fileID: 0}
  HoldableItemInUseLeftHand: {fileID: 0}
  WeaponInUseRightHand: {fileID: 0}
  WeaponInUseLeftHand: {fileID: 0}
  MeleeWeaponInUseRightHand: {fileID: 0}
  MeleeWeaponInUseLeftHand: {fileID: 0}
  AimMode: 0
  FireModeWalkSpeed: 0.5
  FireModeRunSpeed: 1.3
  FireModeCrouchSpeed: 0.5
  IKPositionRightHand: {fileID: 0}
  IKPositionLeftHand: {fileID: 0}
  HumanoidSpine: {fileID: 0}
  RightFootBone: {fileID: 0}
  LeftFootBone: {fileID: 0}
  LeftHandWeightIK: 0
  RightHandWeightIK: 0
  CurrentTimeToDisableFireMode: 0
  VehicleInArea: {fileID: 0}
  CharacterHitBoxes: []
  IsDead: 0
  DisableAllMove: 0
  CanMove: 1
  CanRotate: 1
  IsMoving: 0
  IsRunning: 0
  IsSprinting: 0
  IsCrouched: 0
  IsProne: 0
  CanJump: 0
  IsJumping: 0
  IsGrounded: 1
  IsSliding: 0
  IsMeleeAttacking: 0
  IsPunching: 0
  IsItemEquiped: 0
  IsDualWielding: 0
  IsAiming: 0
  FiringMode: 0
  FiringModeIK: 1
  ToPickupItem: 0
  IsRolling: 0
  IsRagdolled: 0
  IsDriving: 0
  UsedItem: 0
  IsReloading: 0
  WallAHead: 0
  IsWeaponSwitching: 0
  InverseKinematics: 1
  IsArtificialIntelligence: 0
  UseDefaultControllerInput: 0
  AutoRun: 1
  WalkOnRunButton: 1
  DecreaseSpeedOnJump: 0
  BlockVerticalInput: 0
  BlockHorizontalInput: 0
  BlockFireModeOnCursorVisible: 0
  BlockFireModeOnPunching: 1
  EnablePunchAttacks: 1
  EnableRoll: 1
  PhysicalDamage: 0
  DoRagdollPhysicalDamage: 0
  PhysicalDamageStartAt: 25
  PhysicalDamageMultiplier: 0.8
  RagdollStartAtDamage: 10
  PhysicalDamageIgnoreTags:
  - Player
  - Enemy
  - Bones
  - Walls
  - Bullet
--- !u!114 &5307526407119770209
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 18a04a3340ba13a4ea148b476187b539, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  BlockBodyPositioning: 0
  EnableFootPlacement: 1
  AdvancedMode: 1
  GroundLayers:
    serializedVersion: 2
    m_Bits: 0
  RaycastMaxDistance: 6
  RaycastHeight: 3
  FootHeight: 0.1
  MaxStepHeight: 0.6
  UseDynamicFootPlacing: 1
  LeftFootHeightCurveName: LeftFootHeight
  RightFootHeightCurveName: RightFootHeight
  SmoothIKTransition: 1
  FootHeightMultiplier: 0.6
  GlobalWeight: 0.5
  LeftFootHeightFromGround: 0
  RightFootHeightFromGround: 0
  LeftFootRotationWeight: 0
  RightFootRotationWeight: 0
  radius: 0.1
  EnableDynamicBodyPlacing: 1
  UpAndDownForce: 10
  MaxBodyCrouchHeight: 0.65
  JustCalculateBodyPosition: 0
  KeepCharacterOnGround: 0
  RaycastDistanceToGround: 1.2
  BodyHeightPosition: 0.01
  Force: 10
  TheresGroundBelow: 0
  GroundCheckRadius: 0.1
  LastBodyPositionY: 0
  NewAnimationBodyPosition: {x: 0, y: 0, z: 0}
  Animation_Y_BodyPosition: 0
--- !u!114 &1307181696719956790
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9e4c0b46aa90a2448a4767ed100b53de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  audioSource: {fileID: 0}
  FootstepAudioClips: []
  InvertX: 0
  MinTimeToPlayAudio: 0.3
  GroundLayers:
    serializedVersion: 2
    m_Bits: 0
  CheckRadius: 0.1
  UpOffset: -0.07
  ForwardOffset: 0.07
  LeftFoot: {fileID: 8553622461001826082}
  RightFoot: {fileID: 6799646646942304111}
--- !u!114 &6357630325359916132
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b14e47765b621724f99e7ce7bd1602c2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  JUFootPlacer: {fileID: 0}
  RootBone: {fileID: 0}
  RootBoneSpineLean: 1
  RootBoneSpineMovement: 1
  RootBoneLeanIntensity: 30
  RootBoneLeanSpeed: 8
  RootBoneDownMovementIntensity: 0.5
  BlockForwardLeanWeight: 8
  AxisToLean: 0
--- !u!114 &8508650648846955072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5238e6384da25284e8adadb2e9544db5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _startVehicle: {fileID: 0}
  EnterVehiclesEnabled: 1
  ExitVehiclesEnabled: 1
  DisableCharacterOnEnter: 0
  DelayToReenableAction: 0.2
  UseDefaultInputs: 1
  CheckNearVehicles:
    Layer:
      serializedVersion: 2
      m_Bits: 1
    AvoidObstacles: 1
    EnterVehiclesAreaTag: VehicleArea
    CheckRange: 2
    AutoCheck: 1
    AutoCheckInterval: 0.5
  MaxVehicleSpeedToEnter: 3
  MaxVehicleSpeedToExit: 1000
  MaxCharacterSpeedToEnter: 1
  GroundLayer:
    serializedVersion: 2
    m_Bits: 1
  OnEnterVehicle:
    m_PersistentCalls:
      m_Calls: []
  OnExitVehicle:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &4352694967239775742
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 945aee53f47930a488ae796b0af5fb9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Enabled: 1
  FootPlacer: 0
  GroundLayer:
    serializedVersion: 2
    m_Bits: 0
  SpineLean: 1
  LeanDirection: 0.2
  ForwardLeanAxis: 0
  SidesLeanAxis: 2
  InvertForwardLean: 0
  InvertSideLean: 0
--- !u!114 &708415186624860627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6ccb44a366686104fa78d0a7a881b0b1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RagdollEnabled: 0
  State: 0
  AllBones: []
  RagdollBones: []
  Hips: {fileID: 2159168449819813484}
  HipsParent: {fileID: 5883595087999649528}
  BlendAmount: 0
  TimeToGetUp: 3
  BlendSpeed: 2
  RagdollDrag: 0.5
  RagdollWhenPressKeyG: 0
  ViewHumanBodyBones: 0
  ViewBodyPhysics: 0
  ViewBodyDirection: 0
  FilterByBoneLayer: 1
--- !u!114 &7181221774905008198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bd3a90835420dd44ca72e32a3ab2d133, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateOnBodyItemsVisibility: 0
  DisableAllItemsOnStart: 1
  IsALoot: 0
  HoldableItensRightHand: []
  HoldableItensLeftHand: []
  AllHoldableItems: []
  AllItems: []
  SequenceSlot:
  - SelectedSlot: 0
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 1
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 2
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 3
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 4
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 5
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 6
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 7
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 8
    ItemInThisSlot: {fileID: 0}
  - SelectedSlot: 9
    ItemInThisSlot: {fileID: 0}
  EnablePickup: 1
  ItemLayer:
    serializedVersion: 2
    m_Bits: 0
  CheckerOffset: {x: 0, y: 0, z: 0}
  CheckerRadious: 1
  UseDefaultInputToPickUp: 1
  AutoEquipPickedUpItems: 1
  HoldTimeToPickUp: 0.1
  ItemToPickUp: {fileID: 0}
  ItemsAround: []
  WeaponsRightHand: []
  WeaponsLeftHand: []
  HoldableItemInUseInRightHand: {fileID: 0}
  HoldableItemInUseInLeftHand: {fileID: 0}
  WeaponInUseInRightHand: {fileID: 0}
  WeaponInUseInLeftHand: {fileID: 0}
  MeleeWeaponInUseInRightHand: {fileID: 0}
  MeleeWeaponInUseInLeftHand: {fileID: 0}
  CurrentRightHandItemID: -1
  CurrentLeftHandItemID: -1
--- !u!114 &4406486220611190624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c1571549826a8de4ea21dc78474448f9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  IsPlayer: 0
  UseOldInputSystem: 0
  JuTPSCharacter: {fileID: 0}
  ItemToEquipOnStart: -1
  EnableNextAndPreviousWeaponSwitch: 0
  CustomNextWeaponKeyCode: 0
  CustomPreviousWeaponKeycode: 0
  EnableAlphaNumericWeaponSwitch: 0
  EnableMouseScrollWeaponSwitch: 0
  ScrollThreshold: 0.1
--- !u!114 &6501581849669830767
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cc6deb64f70ce214dbaea92026b75c35, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DestroyOnStartGame: 0
  PlayerController: {fileID: 7299334403609959943}
  VisualizationSettings:
    ResourcesPath: 
    GizmosColor: {r: 0, g: 0, b: 0, a: 0.5}
    WireGizmosColor: {r: 0.9, g: 0.4, b: 0.2, a: 0.5}
    StepVisualizerMesh: {fileID: -3964615163347849732, guid: 6b70b21f3295a7441b544a6f7bd820e9,
      type: 3}
  StepCorrection: 1
  GroundCheck: 1
  WallCheck: 1
--- !u!114 &6431247798047166587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fa2549d042f8b6d41bb6289069865f19, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Health: 100
  MaxHealth: 100
  BloodScreenEffect: 0
  BloodHitParticle: {fileID: 0}
  OnDeath:
    m_PersistentCalls:
      m_Calls: []
  IsDead: 0
--- !u!114 &4907944412144306944
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fe91dc294731a9428c0941ea7579110, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  complexMaskingMode: 0
  drawingMode: 1
  outlineLayer: 0
  outlineTargets:
  - CutoutMask: 8
    renderer: {fileID: 4651581321204235329}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  - CutoutMask: 8
    renderer: {fileID: 684149955958306850}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  - CutoutMask: 8
    renderer: {fileID: 4990569073166056540}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  - CutoutMask: 8
    renderer: {fileID: 7327842607408352430}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  - CutoutMask: 8
    renderer: {fileID: 6723119251380532678}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  - CutoutMask: 8
    renderer: {fileID: 7670236032469405206}
    SubmeshIndex: 0
    BoundsMode: 0
    Bounds:
      m_Center: {x: 0, y: 0, z: 0}
      m_Extent: {x: 0.5, y: 0.5, z: 0.5}
    CutoutThreshold: 0.5
    CullMode: 2
    cutoutTextureName: 
    cutoutTextureIndex: 0
  renderStyle: 2
  outlineParameters:
    enabled: 1
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  backParameters:
    enabled: 1
    color: {r: 0, g: 1, b: 0, a: 1}
    dilateShift: 0.1
    blurShift: 0.1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
  frontParameters:
    enabled: 0
    color: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    dilateShift: 1
    blurShift: 1
    fillPass:
      shader: {fileID: 0}
      serializedProperties: []
--- !u!114 &1999539453065640018
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f96242ea5200de41a524b1b321a045f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  CoverTriggerTag: CoverTrigger
  CurrentCoverTrigger: {fileID: 0}
  EnableCover: 1
  AutoMode: 0
  SneakSpeed: 1
  RotationSpeed: 10
  CrouchedSneakSpeed: 1.3
  CornerPositionOffset: 0.5
  WallOffsetPosition: 0
  WallOffsetPositionOnFireMode: 0.2
  CoverAnimatorParameter: Cover
  CameraRightOffset: 0.6
  SwitchSideSpeed: 8
  SpineInclination: -5
  LeftWeaponInclinationIntensity: 5
  RightWeaponInclinationIntensity: 2.5
  LeanAxis: {x: 0, y: 0, z: 1}
  IsCovering: 0
  IsCrouchCover: 0
  IsLeftSide: 1
  OnWallEdge: 0
  FireModeDurationInCover: 0.1
  OnCover:
    m_PersistentCalls:
      m_Calls: []
  OnExitCover:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1596389044794768680
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc1e1f1b144264a4198b451fbb721499, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  bodyTransform: {fileID: 5883595087999649528}
  Destroyed_Effect: {fileID: 0}
  Destroyed_Effect_Offset: {x: 0, y: 0, z: 0}
  isDead: 0
  MainBody_Damage_Threshold: 5
  UnitId: 10002
  UnitLevel: 0
  CurWeaponId: 0
  CurShellId: 0
--- !u!208 &935697178488449131
NavMeshObstacle:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6552009734885710914}
  m_Enabled: 1
  serializedVersion: 3
  m_Shape: 1
  m_Extents: {x: 0.5, y: 0.5, z: 0.5}
  m_MoveThreshold: 0.1
  m_Carve: 1
  m_CarveOnlyStationary: 0
  m_Center: {x: 0, y: 0, z: 0}
  m_TimeToStationary: 0.5
--- !u!1 &6684160924164783300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4405265322804230557}
  m_Layer: 9
  m_Name: Dummy005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4405265322804230557
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6684160924164783300}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000671481, y: 0.0000000033611278, z: 0.08388379, w: 0.9964756}
  m_LocalPosition: {x: 0.430325, y: 0.43978363, z: 0.4843223}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6784458896911320906
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2900052749343573065}
  - component: {fileID: 2715500251680342015}
  - component: {fileID: 2375741786329610827}
  m_Layer: 0
  m_Name: p226_Slide
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2900052749343573065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6784458896911320906}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.0003915865, y: 0.044852935, z: -0.03657893}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7248280167301000214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2715500251680342015
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6784458896911320906}
  m_Mesh: {fileID: -8272425351879887783, guid: e5464b30d0b3cfe4ab0bc99fdc19002d, type: 3}
--- !u!23 &2375741786329610827
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6784458896911320906}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 0cc3cc3352c96014484a0c6a0b56a0ba, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6844882956742673787
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8068520012858600233}
  - component: {fileID: 4651581321204235329}
  - component: {fileID: 6389880827058153802}
  - component: {fileID: 3357483485536454202}
  m_Layer: 9
  m_Name: Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8068520012858600233
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6844882956742673787}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &4651581321204235329
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6844882956742673787}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2afe1fa799571074abb67aab6d4788dd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2959467708922500008, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 5996799717741359484}
  - {fileID: 5081096013438664983}
  - {fileID: 1845932228718614015}
  - {fileID: 3751691097141516430}
  - {fileID: 390046516716207638}
  - {fileID: 8484602595043628219}
  - {fileID: 5735808257124294363}
  - {fileID: 2528228882701661677}
  - {fileID: 2927468740787406367}
  - {fileID: 2721921557697649038}
  - {fileID: 139379554654945687}
  - {fileID: 7262944662503666424}
  - {fileID: 2812878794726008757}
  - {fileID: 1021241047454088735}
  - {fileID: 5612105991538806793}
  - {fileID: 7332885288040405272}
  - {fileID: 8811829750699825161}
  - {fileID: 1193872661686823505}
  - {fileID: 6414533671734520390}
  - {fileID: 561305600587650171}
  - {fileID: 882835569133387009}
  - {fileID: 2159168449819813484}
  - {fileID: 8478144065346117392}
  - {fileID: 2842749508136079699}
  - {fileID: 1886683036069179337}
  - {fileID: 6799646646942304111}
  - {fileID: 9103391460933089211}
  - {fileID: 8627290112246882791}
  - {fileID: 464340304123392555}
  - {fileID: 3529489055650455530}
  - {fileID: 869308139326682320}
  - {fileID: 1162548375716080965}
  - {fileID: 996235153334202700}
  - {fileID: 2646999674787043067}
  - {fileID: 4871632405394784032}
  - {fileID: 8572670108132032713}
  - {fileID: 6376274829410319525}
  - {fileID: 4925751383943211775}
  - {fileID: 647024267804474169}
  - {fileID: 4344151855918140763}
  - {fileID: 3473154600333279016}
  - {fileID: 7443811283614267304}
  - {fileID: 8791853670228756238}
  - {fileID: 3824296566610331598}
  - {fileID: 8553622461001826082}
  - {fileID: 1126074499856568765}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2159168449819813484}
  m_AABB:
    m_Center: {x: 0.0071356297, y: 0.65399146, z: 0.0980576}
    m_Extent: {x: 2.2491007, y: 3.758015, z: 0.9935907}
  m_DirtyAABB: 0
--- !u!114 &6389880827058153802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6844882956742673787}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &3357483485536454202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6844882956742673787}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cfca2857e2554498abdeb7b5f23837f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6980982049542304856
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3529489055650455530}
  m_Layer: 9
  m_Name: mixamorig:LeftForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3529489055650455530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6980982049542304856}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000043998784, y: 0.0009850163, z: -0.00000011537772, w: 0.9999995}
  m_LocalPosition: {x: -0.00000015258789, y: 0.5156063, z: -0.00000030517577}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 869308139326682320}
  m_Father: {fileID: 464340304123392555}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6992551915282520591
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6518118568121173389}
  m_Layer: 9
  m_Name: mixamorig:LeftHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6518118568121173389
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6992551915282520591}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.00048358916, y: 0.041367188, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3473154600333279016}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7046859507310957675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 882835569133387009}
  m_Layer: 9
  m_Name: mixamorig:Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &882835569133387009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7046859507310957675}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12826212, y: 1.3117263e-14, z: 2.3096404e-13, w: 0.9917403}
  m_LocalPosition: {x: -0, y: 0.43419525, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 561305600587650171}
  m_Father: {fileID: 5996799717741359484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7090477107833599408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4683307285138841727}
  m_Layer: 9
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4683307285138841727
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7090477107833599408}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006703807, y: -0.0000000015437521, z: -0.026945444,
    w: 0.99963695}
  m_LocalPosition: {x: 0.5587087, y: 0.7458069, z: 0.4843225}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7191830842799495712
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6414533671734520390}
  m_Layer: 9
  m_Name: mixamorig:RightHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6414533671734520390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7191830842799495712}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000015063561, y: 0.0053345305, z: 0.008239335, w: 0.99995184}
  m_LocalPosition: {x: -0.0008315277, y: 0.074771725, z: -0.0000012207031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8532699542153512865}
  m_Father: {fileID: 139379554654945687}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7194615000169346913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8301692803115576651}
  m_Layer: 9
  m_Name: Bone004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8301692803115576651
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7194615000169346913}
  serializedVersion: 2
  m_LocalRotation: {x: -3.9262804e-16, y: 7.149951e-15, z: 0.17825551, w: 0.98398423}
  m_LocalPosition: {x: -0.63439083, y: 0, z: -0.000000009536743}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1294561579366372785}
  m_Father: {fileID: 350698881400451608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7207395603428807450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4044738204077310158}
  m_Layer: 9
  m_Name: mixamorig:RightHandIndex4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4044738204077310158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7207395603428807450}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.0020085906, y: 0.04133392, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1193872661686823505}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7277083238275563908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9103391460933089211}
  m_Layer: 9
  m_Name: mixamorig:RightToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9103391460933089211
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7277083238275563908}
  serializedVersion: 2
  m_LocalRotation: {x: 0.37401992, y: -0.023162767, z: 0.009344724, w: 0.9270843}
  m_LocalPosition: {x: -0, y: 0.36319312, z: -0.0000000667572}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4145682087461902105}
  m_Father: {fileID: 6799646646942304111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7462446542936222684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7528594999894934019}
  m_Layer: 9
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7528594999894934019
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7462446542936222684}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0007916906, y: -0.00025519502, z: 0.804585, w: 0.59383696}
  m_LocalPosition: {x: 0.7964005, y: 1.6556246, z: 0.1491979}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5666053432917101740}
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7515736779689044191
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 869308139326682320}
  m_Layer: 9
  m_Name: mixamorig:LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &869308139326682320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7515736779689044191}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01890739, y: -0.11859946, z: 0.016023766, w: 0.9926328}
  m_LocalPosition: {x: -0.00000015258789, y: 0.5870236, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4925751383943211775}
  - {fileID: 2646999674787043067}
  - {fileID: 996235153334202700}
  - {fileID: 1162548375716080965}
  m_Father: {fileID: 3529489055650455530}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7569903178749935310
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4925751383943211775}
  m_Layer: 9
  m_Name: mixamorig:LeftHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4925751383943211775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7569903178749935310}
  serializedVersion: 2
  m_LocalRotation: {x: -0.020678738, y: -0.00014159891, z: -0.009789941, w: 0.9997383}
  m_LocalPosition: {x: 0.11117666, y: 0.4030705, z: 0.010189209}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4344151855918140763}
  m_Father: {fileID: 869308139326682320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7704091627010391614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8640813325590577738}
  m_Layer: 9
  m_Name: Dummy010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8640813325590577738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7704091627010391614}
  serializedVersion: 2
  m_LocalRotation: {x: 0.96785784, y: -0.25149807, z: 0.000000042095053, w: -0.0000001266418}
  m_LocalPosition: {x: -0.62375516, y: 0.5049524, z: 0.4843226}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7721055907957963791
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4344206908818970472}
  m_Layer: 9
  m_Name: Bone008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4344206908818970472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721055907957963791}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000028547278, y: -0.0000000044076667, z: -0.15259074,
    w: 0.9882895}
  m_LocalPosition: {x: -0.68477875, y: -0.00000015258789, z: 0.000000038146972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5666053432917101740}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7748617372387840958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1193872661686823505}
  m_Layer: 9
  m_Name: mixamorig:RightHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1193872661686823505
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7748617372387840958}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00004618613, y: 0.0016996464, z: 0.021135943, w: 0.9997752}
  m_LocalPosition: {x: 0.00043689727, y: 0.06946258, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4044738204077310158}
  m_Father: {fileID: 7332885288040405272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7832789128889617241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2643127265135017745}
  m_Layer: 9
  m_Name: mixamorig:LeftHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2643127265135017745
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7832789128889617241}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.0017000961, y: 0.040518645, z: -0.00000061035155}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7443811283614267304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7845782207563850682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5612105991538806793}
  m_Layer: 9
  m_Name: mixamorig:RightHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5612105991538806793
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7845782207563850682}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002035541, y: -0.014728689, z: 0.22239824, w: 0.97484255}
  m_LocalPosition: {x: -0.0075616455, y: 0.0867392, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6155586233555599963}
  m_Father: {fileID: 2721921557697649038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7868820674843746306
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3566285818775239524}
  m_Layer: 9
  m_Name: R_eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3566285818775239524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7868820674843746306}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006705523, y: 9.094945e-13, z: 3.1459638e-13, w: 1}
  m_LocalPosition: {x: 0.4074702, y: 0.6157135, z: 0.7258972}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8015275931075005474
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6376274829410319525}
  m_Layer: 9
  m_Name: mixamorig:LeftHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6376274829410319525
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8015275931075005474}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0024132128, y: 0.019841818, z: -0.20857076, w: 0.97780305}
  m_LocalPosition: {x: 0.0075422665, y: 0.084403075, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9130350769055994120}
  m_Father: {fileID: 8572670108132032713}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8095486615073484027
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9063282056373606929}
  - component: {fileID: 4990569073166056540}
  - component: {fileID: 1670543932185003298}
  m_Layer: 9
  m_Name: Hair
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9063282056373606929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8095486615073484027}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &4990569073166056540
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8095486615073484027}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a06ba77f3f31fba459b3c4d1b3d02161, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -3178576508544696699, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 561305600587650171}
  - {fileID: 7528594999894934019}
  - {fileID: 5666053432917101740}
  - {fileID: 4344206908818970472}
  - {fileID: 7511126210637667772}
  - {fileID: 8301692803115576651}
  - {fileID: 350698881400451608}
  - {fileID: 1294561579366372785}
  - {fileID: 338711804419288855}
  - {fileID: 4418418818962142617}
  - {fileID: 1435930432157879822}
  - {fileID: 7565358681497497537}
  - {fileID: 9082533802662949271}
  - {fileID: 7881030511182108868}
  - {fileID: 2332294850227083599}
  - {fileID: 4356446074273187628}
  - {fileID: 447577384055631140}
  - {fileID: 4349125886652700671}
  - {fileID: 9144868439852211942}
  - {fileID: 779431428164314511}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 561305600587650171}
  m_AABB:
    m_Center: {x: -0.034160137, y: 0.7884383, z: -0.16729903}
    m_Extent: {x: 1.3446593, y: 1.4792576, z: 1.5960982}
  m_DirtyAABB: 0
--- !u!114 &1670543932185003298
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8095486615073484027}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &8114604127403411955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2860550690573192381}
  - component: {fileID: 684149955958306850}
  - component: {fileID: 353250230256338980}
  m_Layer: 9
  m_Name: Face
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2860550690573192381
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8114604127403411955}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 5.1704283, z: -0.24068451}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &684149955958306850
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8114604127403411955}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9945c61ec10ebb2499413d583a1de55e, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 3621554888825754127, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 561305600587650171}
  - {fileID: 6283733638883419277}
  - {fileID: 4683307285138841727}
  - {fileID: 8771635536770140149}
  - {fileID: 4405265322804230557}
  - {fileID: 1917396652922928935}
  - {fileID: 6729750463399930231}
  - {fileID: 3566285818775239524}
  - {fileID: 3323014790432019716}
  - {fileID: 4443393235076110565}
  - {fileID: 6412882093267405258}
  - {fileID: 8640813325590577738}
  - {fileID: 4871670533839514625}
  - {fileID: 2026669618834362001}
  - {fileID: 2844537960111159810}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 561305600587650171}
  m_AABB:
    m_Center: {x: 0.000033915043, y: 0.91967297, z: -0.076529145}
    m_Extent: {x: 0.875177, y: 1.0528371, z: 0.8062761}
  m_DirtyAABB: 0
--- !u!114 &353250230256338980
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8114604127403411955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &8193431650519261982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8627290112246882791}
  m_Layer: 9
  m_Name: mixamorig:LeftShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8627290112246882791
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193431650519261982}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5449465, y: -0.44009438, z: 0.5858106, w: 0.4076472}
  m_LocalPosition: {x: -0.17305231, y: 0.3859857, z: -0.0122303}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 464340304123392555}
  m_Father: {fileID: 5996799717741359484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8229796840709060554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 139379554654945687}
  m_Layer: 9
  m_Name: mixamorig:RightHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &139379554654945687
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8229796840709060554}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000013875751, y: -0.00014080471, z: -0.010121499, w: 0.9999488}
  m_LocalPosition: {x: 0.0006525421, y: 0.07151672, z: -0.0000012207031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6414533671734520390}
  m_Father: {fileID: 2927468740787406367}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8288973036213639876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1917396652922928935}
  m_Layer: 9
  m_Name: Dummy006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1917396652922928935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8288973036213639876}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007433489, y: -0.000000016208848, z: -0.30255768, w: 0.95313114}
  m_LocalPosition: {x: 0.25246918, y: 0.46381682, z: 0.4843223}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8323675429830954002
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5735808257124294363}
  m_Layer: 9
  m_Name: mixamorig:RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5735808257124294363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8323675429830954002}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00956443, y: 0.13741706, z: -0.009701544, w: 0.9904196}
  m_LocalPosition: {x: -0, y: 0.5870121, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2812878794726008757}
  - {fileID: 7262944662503666424}
  - {fileID: 2927468740787406367}
  - {fileID: 2528228882701661677}
  m_Father: {fileID: 8484602595043628219}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8438396783453891853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4871670533839514625}
  m_Layer: 9
  m_Name: Dummy011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4871670533839514625
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8438396783453891853}
  serializedVersion: 2
  m_LocalRotation: {x: 0.9964756, y: -0.08388389, z: 0.0000000122153025, w: -0.00000013260075}
  m_LocalPosition: {x: -0.48185384, y: 0.4397833, z: 0.4843226}
  m_LocalScale: {x: -1, y: -1, z: -1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8489160644162792173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 996235153334202700}
  m_Layer: 9
  m_Name: mixamorig:LeftHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &996235153334202700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8489160644162792173}
  serializedVersion: 2
  m_LocalRotation: {x: -0.020651266, y: -0.0028575498, z: -0.034800105, w: 0.99917686}
  m_LocalPosition: {x: -0.12229686, y: 0.37677306, z: 0.0011386108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4871632405394784032}
  m_Father: {fileID: 869308139326682320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8527942380020888618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8228817392633141322}
  - component: {fileID: 3864841729238796841}
  m_Layer: 9
  m_Name: Big Weapon Position Reference
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8228817392633141322
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8527942380020888618}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07306647, y: 0.06705522, z: -0.7331321, w: 0.6728166}
  m_LocalPosition: {x: 0.207, y: 0.14, z: 0.24}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7125183737757033826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3864841729238796841
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8527942380020888618}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b9259320348a3e4fa0152855cf2d5f3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Draw: 1
  ModelToDraw: 2
  DrawMode: 2
  GizmoColor: {r: 0.3, g: 0.5, b: 1, a: 0.7}
  WireframeColor: {r: 0.5, g: 0.8, b: 1, a: 0.1}
  MirrorX: 1
--- !u!1 &8690052054195356821
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8532699542153512865}
  m_Layer: 9
  m_Name: mixamorig:RightHandRing4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8532699542153512865
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8690052054195356821}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.00017898559, y: 0.03335663, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6414533671734520390}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8963890671701923041
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8791853670228756238}
  m_Layer: 9
  m_Name: mixamorig:LeftHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8791853670228756238
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8963890671701923041}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000024067413, y: -0.0002167062, z: 0.015314625, w: 0.9998827}
  m_LocalPosition: {x: -0.00033901213, y: 0.070954435, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4049274269355597357}
  m_Father: {fileID: 647024267804474169}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8967100935604413203
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3824296566610331598}
  m_Layer: 9
  m_Name: mixamorig:LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3824296566610331598
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8967100935604413203}
  serializedVersion: 2
  m_LocalRotation: {x: -0.099873975, y: -0.0025542143, z: 0.029280407, w: 0.9945659}
  m_LocalPosition: {x: 0.00000015258789, y: 1.2198901, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8553622461001826082}
  m_Father: {fileID: 8478144065346117392}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9007941517841961625
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7248280167301000214}
  - component: {fileID: 6056825312342214853}
  m_Layer: 0
  m_Name: P226 PISTOLA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7248280167301000214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9007941517841961625}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6644516247294459313}
  - {fileID: 9078839242738391539}
  - {fileID: 7940277349427510050}
  - {fileID: 2900052749343573065}
  m_Father: {fileID: 2898793082032515967}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6056825312342214853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9007941517841961625}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d36b4ea97d3749d43a1b9da61bdd8bc2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Radious: 2000
  Angle: 360
  Precision: 0.5
  LossOfAccuracyPerShot: 1
  BulletsPerMagazine: 10
  ReloadSpeed: 0.5
  AP_Bullet_Prefab: {fileID: 1459669234869594, guid: 2b4848108857e264f832cb19d952938d,
    type: 3}
--- !u!1 &9012227723736906773
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6283733638883419277}
  m_Layer: 9
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6283733638883419277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9012227723736906773}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007455831, y: 0.0000000044814796, z: 0.08940544, w: 0.99599534}
  m_LocalPosition: {x: 0.38327038, y: 0.74099, z: 0.5722082}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9054772981026171710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8478144065346117392}
  m_Layer: 9
  m_Name: mixamorig:LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8478144065346117392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9054772981026171710}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00035684102, y: 0.031049922, z: 0.9994243, w: 0.013669266}
  m_LocalPosition: {x: -0.32129496, y: -0.1554718, z: 0.007489624}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3824296566610331598}
  m_Father: {fileID: 2159168449819813484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9069920129074695767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7332885288040405272}
  m_Layer: 9
  m_Name: mixamorig:RightHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7332885288040405272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9069920129074695767}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000019014438, y: -0.0013700742, z: 0.02027696, w: 0.99979347}
  m_LocalPosition: {x: -0.002445526, y: 0.07132614, z: -0.0000012207031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1193872661686823505}
  m_Father: {fileID: 2812878794726008757}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9085173712692466903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3356139804833992853}
  m_Layer: 9
  m_Name: mixamorig:HeadTop_End
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3356139804833992853
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9085173712692466903}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -7.4505804e-11, y: 1.8054941, z: 0.4442927}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 561305600587650171}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9147882034462517999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3016230864625929017}
  - component: {fileID: 6723119251380532678}
  - component: {fileID: 8128345539957826665}
  m_Layer: 9
  m_Name: Mouth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3016230864625929017
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147882034462517999}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 4.4096684, z: 0.4477834}
  m_LocalScale: {x: 0.903812, y: 0.903812, z: 0.903812}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5883595087999649528}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6723119251380532678
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147882034462517999}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: ae145d568e7a63b438777bb1cdcb8f3d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 3205107595041152940, guid: 2cb8d89e073c92d48a79a7ac9d9a047e, type: 3}
  m_Bones:
  - {fileID: 561305600587650171}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 561305600587650171}
  m_AABB:
    m_Center: {x: 0.00003388524, y: 0.15891314, z: 0.61193883}
    m_Extent: {x: 0.25608593, y: 0.18274277, z: 0.103770256}
  m_DirtyAABB: 0
--- !u!114 &8128345539957826665
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9147882034462517999}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 034bc47afcbf5e141a58d5c20b7808e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &9175604637474532298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1021241047454088735}
  m_Layer: 9
  m_Name: mixamorig:RightHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1021241047454088735
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9175604637474532298}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000010059653, y: 0.00020329082, z: 0.009560817, w: 0.9999543}
  m_LocalPosition: {x: -0.00041778563, y: 0.06718521, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8811829750699825161}
  m_Father: {fileID: 7262944662503666424}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
