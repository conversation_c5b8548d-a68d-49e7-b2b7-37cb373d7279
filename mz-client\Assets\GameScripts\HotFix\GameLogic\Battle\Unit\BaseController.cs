﻿using UnityEngine;

namespace GameLogic
{
    public abstract class BaseController
    {
        protected Transform BodyTransform;

        protected UnitActor UnitActor;

        // 攻击行为
        public IAttackBehavior AttackBehavior { get; protected set; }

        public abstract void Initialize(UnitActor unitActor);

        /// <summary>
        /// 移动至目标点
        /// </summary>
        /// <param name="targetPosition"></param>
        public abstract void MoveToTarget(Vector3 targetPosition);

        /// <summary>
        /// 恢复移动
        /// </summary>
        public abstract void RestoreMove();

        public abstract void PerformAttack();
        public abstract void StopMovement();
        public abstract void Stop();

        /// <summary>
        /// 设置移动速度
        /// </summary>
        /// <param name="speed"></param>
        public abstract void SetMoveSpeed(float speed);

        public abstract void SetRunningState(bool runningState);

        /// <summary>
        /// 获取移动速度
        /// </summary>
        /// <returns></returns>
        public abstract float GetMoveSpeed();

        public abstract bool HasReachedDestination();

        /// <summary>
        /// 获取移动状态
        /// </summary>
        /// <returns></returns>
        public abstract bool GetMoveStatu();
    }

    public interface IAttackBehavior
    {
        void OnAttackStart(Transform target);
        void OnAttackSustain();
        void OnAttackEnd();
        void AttackInDirection(Transform camTrans);
        void EjectionAttack(Vector3 target, uint uuid);
    }
}