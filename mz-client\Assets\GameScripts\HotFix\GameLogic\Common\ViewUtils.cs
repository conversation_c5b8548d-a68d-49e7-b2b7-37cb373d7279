using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameBase;
using GameCommon;
using Google.Protobuf.Collections;
using Luban;
using Message;
using MessagePack;
using TEngine;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

namespace GameLogic
{
    public class ViewUtils
    {
        /// <summary>
        /// 移除所有子节点
        /// </summary>
        /// <param name="_gameobject"></param>
        public static void RemoveAllChildren(Transform _gameobject)
        {
            if (_gameobject == null)
            {
                Log.Warning("clear children node error : parent node is null");
                return;
            }

            int childCount = _gameobject.childCount;
            for (int i = 0; i < childCount; i++)
            {
                var aa = _gameobject.GetChild(0);
                GameObject.DestroyImmediate(aa.gameObject);
            }
        }

        /// <summary>
        /// 进入大厅
        /// </summary>
        public static void EnterHall()
        {
            BattleContext.AllUnits.Clear();
            GameModule.UI.ShowUIAsync<UILoading>(5f);
            GameModule.UI.CloseAllWithOut<UILoading>();
            GameModule.Scene.LoadScene("scene_hall", LoadSceneMode.Single, false, 100, (_sceneNode) =>
            {
                HallSystem.Instance.LoadHall().Forget();
                DOVirtual.DelayedCall(0.35f, () => { GameEvent.Send(ActorEventDefine.Loading, 1); });
            }).ToUniTask();
        }

        public static void EnterGame(int _level_id, int _team_id, RepeatedField<SBattleUnit> _units)
        {
            BattleContext.AllUnits.Clear();
            GameModule.UI.ShowUIAsync<UILoading>(5f);
            GameModule.UI.CloseAllWithOut<UILoading>();
            GameModule.Scene.LoadScene("scene_game", LoadSceneMode.Single, false, 100, (_sceneNode) =>
            {
                BattleSystem.Instance.LoadRoom(_level_id, _team_id, _units).Forget();
                DOVirtual.DelayedCall(0.35f, () => { GameEvent.Send(ActorEventDefine.Loading, 1); });
            }).ToUniTask();
        }

        public async static UniTask InitTable()
        {
            TableInfoReq req = new TableInfoReq { Configs = new() };
            foreach (var item in TableMgr.Instance.TableNameList)
            {
                // if (item.Key != "道具表") continue;
                var version = TableMgr.Instance.GetTableVersion(item.Key);
                if (version == "")
                    version = "0";

                req.Configs.Add(new MPackConfigInfo { TableName = item.Value, Version = version });
            }

            var payload = MessagePackSerializer.Serialize(req);
            var respContent = await GameNet.Http.HttpRequestMPack("game_config", payload);
            if (respContent == null)
            {
                Debug.LogWarning("数据失效，请重新登录");
                return;
            }

            var aa = MessagePackSerializer.Deserialize<TableInfoResp>(respContent);
            if (aa.Msg == "update")
            {
                foreach (var item in aa.Configs)
                {
                    SaveFile($"{item.TableName}.bytes", item.MPackData);
                }

                var tables = new TableModule.Tables(LoadByteBuf);
                TableMgr.Instance.InitTableData(tables);
            }
            else
            {
                var tables = new TableModule.Tables(LocalLoadByteBuf);
                TableMgr.Instance.InitTableData(tables);
            }
        }

        // 加载ByteBuf
        private static ByteBuf LocalLoadByteBuf(string file)
        {
            byte[] fileData = new byte[0];
            var filePath = Path.Combine(Application.persistentDataPath, GlobalDefined.CacheFileName, $"{file}.bytes");
            if (File.Exists(filePath))
            {
                fileData = File.ReadAllBytes(filePath);
            }
            else
            {
                TextAsset localFile = Resources.Load<TextAsset>($"TableFiles/{file}");
                fileData = localFile.bytes;
            }

            return new ByteBuf(fileData);
        }


        // 加载ByteBuf
        private static ByteBuf LoadByteBuf(string file)
        {
            byte[] fileData = new byte[0];
            var filePath = Path.Combine(Application.persistentDataPath, GlobalDefined.CacheFileName, $"{file}.bytes");
            if (File.Exists(filePath))
            {
                fileData = File.ReadAllBytes(filePath);
            }
            else
            {
                Debug.LogWarning($"文件不存在：{filePath}");
            }

            return new ByteBuf(fileData);
        }

        // 保存文件
        private static void SaveFile(string fileName, byte[] fileData)
        {
            // 如果文件名为空或者文件数据为空，则返回
            if (string.IsNullOrWhiteSpace(fileName) || fileData == null || fileData.Length == 0)
                return;

            var directoryPath = Path.Combine(Application.persistentDataPath, GlobalDefined.CacheFileName);
            if (!Directory.Exists(directoryPath))
                Directory.CreateDirectory(directoryPath);
            var filePath = Path.Combine(directoryPath, fileName);

            // 如果文件存在，则删除文件
            if (File.Exists(filePath))
                File.Delete(filePath);

            // 将文件数据写入文件
            File.WriteAllBytes(filePath, fileData);
        }

        /// <summary>
        /// 转化服务器奖励字符串为客户端显示用的字典
        /// </summary>
        /// <param name="_rewards">获得的道具奖励</param>
        /// <param name="_isAdd">是否增加到角色道具信息</param>
        /// <returns></returns>
        public static Dictionary<int, long> TransitionReward(string _rewards, bool _isAdd = false)
        {
            Dictionary<int, long> _props = new Dictionary<int, long>();
            if (_rewards != "0")
            {
                var rewards = _rewards.Split(";");
                foreach (var item in rewards)
                {
                    if (!item.Contains(","))
                    {
                        Debug.LogWarning($"道具奖励格式错误 {rewards}");
                        continue;
                    }

                    var _item = item.Split(",");
                    var propId = int.Parse(_item[0]);
                    long propNum = 0; // 默认值为0
                    if (!long.TryParse(_item[1], out propNum))
                    {
                        // 解析失败，propNum 保持为 0
                        Debug.LogWarning($"无法解析 '{_item[1]}' 为 long 类型，默认值设置为 0");
                    }

                    _props.Add(propId, propNum);
                    if (_isAdd)
                    {
                        if (UserDataModule.Instance.Props.ContainsKey(propId))
                            UserDataModule.Instance.Props[propId] += propNum;
                        else
                            UserDataModule.Instance.Props.Add(propId, propNum);
                    }
                }
            }

            return _props;
        }

        /// <summary>
        /// 将道具添加进角色道具信息
        /// </summary>
        /// <param name="rewards">获得的道具奖励</param>
        /// <returns></returns>
        public static void AddPropsToPlayer(Dictionary<int, long> rewards)
        {
            foreach (var item in rewards)
            {
                if (UserDataModule.Instance.Props.ContainsKey(item.Key))
                    UserDataModule.Instance.Props[item.Key] += item.Value;
                else
                    UserDataModule.Instance.Props.Add(item.Key, item.Value);
            }
        }

        /// <summary>
        /// 十六进制颜色值转换为 Color
        /// </summary>
        /// <param name="hex">16进制数</param>
        /// <param name="alpha">不透明度 0-1</param>
        /// <returns></returns>
        public static Color HexToColor(string hex, float alpha = 1f)
        {
            // 如果是以 "#" 开头，则去掉 "#"
            hex = hex.TrimStart('#');

            // 使用范围索引器来提取 RGB 的子字符串并转换为字节
            var r = Convert.ToByte(hex[..2], 16);
            var g = Convert.ToByte(hex[2..4], 16);
            var b = Convert.ToByte(hex[4..6], 16);

            // 返回 RGB 和 Alpha 值
            return new Color(r / 255f, g / 255f, b / 255f, alpha);
        }

        /// <summary>
        /// 显示弹窗道具的通用方法
        /// </summary>
        /// <param name="props">道具数据</param>
        /// <param name="isAdd">是否增加到角色道具信息</param>
        /// <returns></returns>
        public static void ShowProp(Dictionary<int, long> props, bool isAdd = false)
        {
            // 使用 LINQ 和 string.Join 格式化输出字典内容

            // 使用 StringBuilder 来构建格式化的字符串
            var formattedOutput = new StringBuilder();
            formattedOutput.Append("当前道具-> [");

            var first = true;
            foreach (var kv in props)
            {
                if (!first)
                {
                    formattedOutput.Append(", ");
                }

                formattedOutput.Append($"{kv.Key}:{kv.Value}");
                first = false;
            }

            formattedOutput.Append("]");

            // 打印格式化后的字符串
            Debug.Log(formattedOutput.ToString());

            // 如果需要，将道具添加到角色道具信息中
            if (isAdd)
            {
                AddPropsToPlayer(props);
            }
        }


        /// <summary>
        /// 显示弹窗道具--从服务器返回的SProp数据
        /// </summary>
        /// <param name="props">道具数据</param>
        /// <param name="isAdd">添加到背包 默认True</param>
        /// <returns></returns>
        public static void ShowPropBySProp(RepeatedField<SProp> props, bool isAdd = true)
        {
            // 累加相同 PropId 的 PropQuantity
            var tempList = props
                .GroupBy(x => x.PropId)
                .Select(g => new SProp
                {
                    PropId = g.Key,
                    PropQuantity = g.Sum(x => x.PropQuantity)
                }) // 这里将每个分组的 PropId 和 累加的数量转成 Sprop 对象
                .ToList();

            if (isAdd)
            {
                foreach (var item in tempList)
                {
                    if (UserDataModule.Instance.Props.ContainsKey(item.PropId))
                        UserDataModule.Instance.Props[item.PropId] += item.PropQuantity;
                    else
                        UserDataModule.Instance.Props.Add(item.PropId, item.PropQuantity);
                }
            }

            GameModule.UI.ShowUI<DialogTipsAward1>(tempList);

            // ShowProp(tempDic, true);
        }

        /// <summary>
        /// 创建模型视图渲染器
        /// </summary>
        /// <param name="modelPath">模型路径--名称</param>
        /// <param name="rawImage">UI对象</param>
        /// <param name="offset">整体偏移量</param>
        /// <param name="animaState">动画状态</param>
        public static void CreateCreate3DNodeRender(string modelPath, RawImage rawImage, Vector3 offset, string animaState = "")
        {
            var modelViewRenderer = new Common3DNodeView(modelPath, rawImage, offset, animaState);
        }

        public static bool IsNullOrEmpty(string _s)
        {
            return _s == null || _s.Length == 0;
        }

        /// <summary>
        /// 跳转充值页面
        /// </summary>
        public static void GoRechargePage()
        {
            GameModule.UI.ShowUI<UIShopSubMain>("1");
        }

        /// <summary>
        /// 跳转贸易页面
        /// </summary>
        public static void GoTradePage()
        {
            GameModule.UI.ShowUI<UIShopSubMain>("4");
        }

        /// <summary>
        /// 获取道具图标 若图标不存在使用默认道具图标
        /// </summary>
        /// <param name="propId"></param>
        /// <param name="imgType"></param>
        /// <returns></returns>
        public static Sprite GetPropIcon(int propId, int imgType = 1)
        {
            var hasAsset = GameModule.Resource.HasAsset($"prop_{imgType}_{propId}");
            if (hasAsset == HasAssetResult.NotExist || hasAsset == HasAssetResult.Valid)
            {
                Debug.LogError($"{propId}没有对应的图标, 使用默认ID->101001");
                propId = 101001;
            }

            return GameModule.Resource.LoadAsset<Sprite>($"prop_{imgType}_{propId}");
        }

        /// <summary>
        /// 显示错误日志
        /// </summary>
        public static void ShowErrorLog(ErrorCode errorCode)
        {
            var errorMsg = errorCode switch
            {
                ErrorCode.Success => "成功",
                ErrorCode.SysSessionExpiry => "会话失效",
                ErrorCode.Kicking => "玩家被踢出",
                ErrorCode.AccountNoexist => "账号不存在",
                ErrorCode.PasswordError => "密码错误",
                ErrorCode.VersionError => "版本错误",
                ErrorCode.DefaultErrorCode => "默认错误码",
                ErrorCode.PlayerIsOtherLogin => "玩家在别处登录",
                ErrorCode.LoginAccountPasswordError => "用户名或密码错误",
                ErrorCode.LoginAccountNullity => "账号冻结",
                ErrorCode.LoginBindingOtherMachine => "绑定其它机器",
                ErrorCode.LoginSystemEnjoinLogon => "由于系统维护，暂时停止游戏系统的登录服务，请留意网站公告信息！",
                ErrorCode.LoginSystemEnjoinLogonIp => "抱歉地通知您，系统禁止了您所在的 IP 地址的登录功能，请联系客户服务中心了解详细情况！",
                ErrorCode.LoginSystemEnjoinLoginMachine => "抱歉地通知您，系统禁止了您的机器的登录功能，请联系客户服务中心了解详细情况！",
                ErrorCode.RegisterSystemEnjoin => "注册暂停 提示",
                ErrorCode.LoginSystemEnjoin => "登陆暂停 提示",
                ErrorCode.RegisterSystemForbidName => "抱歉地通知您，您所输入的登录帐号名含有限制字符串，请更换帐号名后再次申请帐号！",
                ErrorCode.RegisterSystemForbidNickname => "抱歉地通知您，您所输入的昵称名含有限制字符串，请更换帐号名后再次申请帐号！",
                ErrorCode.RegisterSystemForbidIp => "抱歉地通知您，系统禁止了您所在的 IP 地址的注册功能，请联系客户服务中心了解详细情况！",
                ErrorCode.RegisterSystemForbidMachine => "抱歉地通知您，系统禁止了您的机器的注册功能，请联系客户服务中心了解详细情况！",
                ErrorCode.RegisterSystemForbidAccountsReg => "此账号已被注册，请换另一账号尝试再次注册！",
                ErrorCode.RegisterSystemForbidNicknameReg => "此昵称已被注册，请换另一昵称尝试再次注册！",
                ErrorCode.LoginGameTokenError => "TOKEN 失效",
                ErrorCode.GameChangeteamError => "换队失败",
                ErrorCode.GameShoppingtradeError => "交易失败",
                ErrorCode.GameMissionInfoError => "游戏任务信息错误",
                ErrorCode.GameUpgradeHavebeenError => "改造接口-已升级",
                ErrorCode.GameUpgradeLimitError => "改造接口-升级条件不足",
                ErrorCode.GameConstructInfoError => "建造接口-建造信息错误",
                ErrorCode.GameConstructRoleError => "建造接口-建造角色错误",
                ErrorCode.GameEmailUnclaimedError => "邮件接口-未领取邮件",
                ErrorCode.GameFuncError => "功能接口-功能错误",
                ErrorCode.GameFuncInfoError => "功能接口-功能信息错误",
                ErrorCode.GameRoleInfoError => "角色接口-角色信息错误",
                ErrorCode.GameMaterialsError => "材料接口-材料错误",
                ErrorCode.GameBattleLevelError => "关卡数据错误",
                ErrorCode.GameBattleBegun => "战斗已经开始--正在重新进入",
                _ => "未知错误"
            };

            Log.Error($"{errorMsg}--{errorCode}");
            GameModule.UI.ShowUIAsync<DialogTipsText>(errorMsg);
        }

        /// <summary>
        /// 获取角色皮肤名称
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public static string GetRoleSkinName(int roleId)
        {
            if (roleId < GlobalDefined.RoleIdSegmentation1)
            {
                roleId = roleId / 10000 * 10000 + roleId % 1000;
                var roleInfoData = UserDataModule.Instance.Roles.Values.FirstOrDefault(p => p.RoleId == roleId);
                if (roleInfoData is null)
                {
                    Debug.LogError($"角色{roleId}信息为空, 使用默认皮肤10002001");
                    return "10002001";
                }

                // 角色头像 若当前皮肤为空选用默认头像
                return IsNullOrEmpty(roleInfoData.CurSkin) ? $"{roleId}001" : roleInfoData.CurSkin;
            }
            else
            {
                return roleId.ToString() + "001";
            }
        }

        /// <summary>
        /// 获取货币简短描述
        /// </summary>
        /// <param name="propId">货币ID</param>
        public static string GetShortDesc(int propId)
        {
            return propId switch
            {
                101001 => "您可以用银币进行大多数消费，也可与其他势力进行贸易，但有些更高级的商品需要黄金储备才可购买。",
                101002 => "这些亮闪闪的宝藏能让您购买华丽的时装、重要的战略物资等。",
                101003 => "这些小小的晶球是远古科技的一部分，其内储存着巨额的能量。只需吃下几颗，就足以支持人格载体进行长途奔袭与高强度的战斗。",
                _ => "未定义的货币"
            };
        }


        /// <summary>
        ///  获取货币提示弹窗显示位置
        /// </summary>
        /// <param name="currIconTrans"></param>
        /// <returns></returns>
        public static Vector3 GetCurrencyPosition(Transform currIconTrans)
        {
            var verticalSpace = 100f;
            var rectTransform = currIconTrans.GetComponent<RectTransform>();

            // 计算 UI 元素的真实中心点（世界坐标）
            var worldCenter = rectTransform.position
                              + (Vector3)(rectTransform.rect.size * rectTransform.lossyScale * (rectTransform.pivot - new Vector2(0.5f, 0.5f)));
            worldCenter.y -= verticalSpace;

            return worldCenter;
        }

        /// <summary>
        /// 将文本转为带省略号的文本
        /// 多余部分使用省略号表示
        /// </summary>
        /// <param name="uiText">Text组件</param>
        /// <param name="content">正文</param>
        /// <returns></returns>
        public static string GetClampedTextWithEllipsis(Text uiText, string content)
        {
            var generator = new TextGenerator();
            var settings = uiText.GetGenerationSettings(uiText.rectTransform.rect.size);

            // 首先判断原文本是否能完整显示
            generator.Populate(content, settings);
            if (generator.characterCountVisible >= content.Length)
            {
                // 能完整显示，不需要省略号
                return content;
            }

            var result = "";
            // 否则裁剪 + ...
            for (var i = content.Length - 1; i >= 0; i--)
            {
                var sub = content[..i] + "...";
                generator.Populate(sub, settings);
                if (generator.characterCountVisible >= i + 3) // +3 是 "..."
                {
                    result = sub;
                    break;
                }
            }

            // 如果实在太短，返回最简省略
            return result;
        }

        /// <summary>
        /// 获取图片Sprite 图片不存在则使用默认图片
        /// </summary>
        public static Sprite GetImageSprite(string imageName, string _defaultImgName = "BattleO-BaC")
        {
            var hasAsset = GameModule.Resource.HasAsset(imageName);
            if (hasAsset == HasAssetResult.NotExist || hasAsset == HasAssetResult.Valid)
            {
                Debug.LogWarning($"{imageName}没有对应的图片, 使用默认图片");
                imageName = _defaultImgName;
            }

            return GameModule.Resource.LoadAsset<Sprite>(imageName);
        }
    }
}