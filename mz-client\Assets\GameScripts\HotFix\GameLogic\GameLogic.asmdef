{"name": "GameLogic", "rootNamespace": "GameLogic", "references": ["GUID:6055be8ebefd69e48b49212b09b47b2f", "GUID:24c092aee38482f4e80715eaa8148782", "GUID:8f58f15387c7a6f4fad9857024eb47f7", "GUID:cbb0d51b565003841ae81cdbaf747114", "GUID:287900cb332e7f546becb86cd2857363", "GUID:8c4dd21966739024fbd72155091d199e", "GUID:f51ebe6a0ceec4240a699833d6309b23", "GUID:e34a5702dd353724aa315fb8011f08c3", "GUID:4307f53044263cf4b835bd812fc161a4", "GUID:9efdcc009eaefad488c9da5b9dc8c70b", "GUID:1fd4edcf510b0a240a726778f7eff6cd", "GUID:75469ad4d38634e559750d17036d5f7c", "GUID:57d1f35b24ae46b4b9d0c4b8184aa224", "GUID:15fc0a57446b3144c949da3e2b9737a9", "GUID:df380645f10b7bc4b97d4f5eb6303d95"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.render-pipelines.universal", "expression": "", "define": "ENABLE_URP"}], "noEngineReferences": false}