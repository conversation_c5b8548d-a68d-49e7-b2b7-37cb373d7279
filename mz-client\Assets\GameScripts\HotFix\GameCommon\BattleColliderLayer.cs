namespace GameCommon
{
    public class BattleColliderLayer
    {
        public const int Character = 9; // 角色
        public const int Bullet = 10; // 炮弹
        public const int Terrain = 11; // 地形
        public const int Bones = 15; // 骨骼
        public const int Wheels = 19; // 轮子
        public const int Reinforce = 20; // 强化件
        public const int Body = 21; // 敌军通用身体部位
        public const int WarPlane = 22; // 战机
        public const int Armor_Collider = 23; // 盔甲
        public const int Extra_Collider = 24; // for Extra Collier.
        public const int Part_Head_Collider = 25; // 部件碰撞器 头
        public const int Part_Arm_Collider = 26; // 部件碰撞器 臂
        public const int Part_Leg_Collider = 27; // 部件碰撞器 腿

        // Layer Mask settings.
        public const int Layer_Mask = ~((1 << 2) + (1 << BattleColliderLayer.Reinforce) + (1 << BattleColliderLayer.Bullet) + (1 << BattleColliderLayer.Extra_Collider)); // Ignore "Layer 2(Ignore Ray)", "BattleColliderLayer.Reinforce", "BattleColliderLayer.Bullet", "BattleColliderLayer.Extra_Collider".
        public const int Aiming_Layer_Mask = ~((1 << 2) + (1 << BattleColliderLayer.Wheels) + (1 << BattleColliderLayer.Reinforce) + (1 << BattleColliderLayer.Bullet) + (1 << BattleColliderLayer.Extra_Collider)); // Ignore "Layer 2(Ignore Ray)", "BattleColliderLayer.Wheels", "BattleColliderLayer.Reinforce", "BattleColliderLayer.Bullet", "BattleColliderLayer.Extra_Collider".
        public const int Anti_Slipping_Layer_Mask = ~((1 << 2) + (1 << BattleColliderLayer.Reinforce) + (1 << BattleColliderLayer.Body) + (1 << BattleColliderLayer.Extra_Collider)); // Ignore "Layer 2(Ignore Ray)", "BattleColliderLayer.Reinforce", "BattleColliderLayer.Body", "BattleColliderLayer.Extra_Collider".
        public const int Detect_Body_Layer_Mask = 1 << BattleColliderLayer.Body; // Hit only "BattleColliderLayer.Body". (Used for detecting a tank)

    }
}
