﻿using System;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using GameBase;
using TEngine;
using UnityEngine;
using UnityEngine.UI;

namespace GameLogic
{
    public class UIBattleMainPage : UIWindow
    {
        private UnitActor _leaderUnit;
        private UIBattleLeaderItem _leaderItem;
        private UIBattleTargetSelect _targetSelect;
        private bool _isHideUI;
        private string m_currentCmdTypeName;

        #region 脚本工具生成的代码

        private Button m_btnChangeRoleSpeed;
        private Text m_textSpeedLv;
        private Transform m_tfSpeedLvContent;
        private Toggle m_togNightVision;
        private Toggle m_togHotVision;
        private Button m_btnClock;
        private Transform m_tfTimeLvContent;
        private Text m_textTimer;
        private Button m_btnAchi;
        private Button m_btnSetting;
        private Button m_btnTps;
        private Toggle m_togTacticalSupport;
        private Transform m_tfTacticalSupportContent;
        private Toggle m_togCmdType_1;
        private Toggle m_togCmdType_2;
        private Toggle m_togCmdType_3;
        private Toggle m_togCmdType_4;

        private Image m_imgOffTypeIcon_1;
        private Image m_imgOffTypeIcon_2;
        private Image m_imgOffTypeIcon_3;
        private Image m_imgOffTypeIcon_4;

        private Transform m_tfCmdContent;
        private Button m_btnSelectAll;
        private Transform m_tfHeadContent;
        private GameObject m_itemLeaderContent;

        private Toggle m_togSupportItem_1;
        private Toggle m_togSupportItem_2;
        private Toggle m_togSupportItem_3;
        private Toggle m_togSupportItem_4;
        private Toggle m_togArcLine;

        private Transform m_tfHpBarContent;
        private Button m_btnStartGame;

        private Button m_btnPrevStep;
        private Button m_btnSupportRocker;

        private GameObject m_itemTargetSelect;
        private Button m_btnReturnLeaderView;

        // TPS射击模式
        private Transform m_tfShootMode;
        private Transform m_tfBulletContent;
        private Button m_btnFire;
        private Button m_btnReturnLeaderMode;
        private Button m_btnFireSecond;
        private Image m_imgAimIcon;
        private Button m_btnFps;

        // FPS射击模式
        private Transform m_tfFpsMode;
        private Button m_btnExitAimState;
        private Button m_btnFireTps;
        private Image m_imgTpsCrossHair;

        protected override void ScriptGenerator()
        {
            m_btnChangeRoleSpeed = transform.Find("BottomLeft/SpeedModule/m_btnChangeRoleSpeed").GetComponent<Button>();
            m_textSpeedLv = transform.Find("BottomLeft/SpeedModule/m_btnChangeRoleSpeed/m_textSpeedLv").GetComponent<Text>();
            m_tfSpeedLvContent = transform.Find("BottomLeft/SpeedModule/m_tfSpeedLvContent");
            m_togNightVision = transform.Find("TopLeft/m_togNightVision").GetComponent<Toggle>();
            m_togHotVision = transform.Find("TopLeft/m_togHotVision").GetComponent<Toggle>();
            m_btnClock = transform.Find("TopRight/m_btnClock").GetComponent<Button>();
            m_tfTimeLvContent = transform.Find("TopRight/m_tfTimeLvContent");
            m_btnAchi = transform.Find("TopRight/m_btnAchi").GetComponent<Button>();
            m_btnSetting = transform.Find("TopRight/m_btnSetting").GetComponent<Button>();
            m_btnTps = transform.Find("BottomRight/SkillContent/m_btnTps").GetComponent<Button>();
            m_togTacticalSupport = transform.Find("BottomRight/m_togTacticalSupport").GetComponent<Toggle>();
            m_tfTacticalSupportContent = transform.Find("BottomRight/m_tfTacticalSupportContent");
            m_togSupportItem_1 = m_tfTacticalSupportContent.Find("m_togSupportItem_1").GetComponent<Toggle>();
            m_togSupportItem_2 = m_tfTacticalSupportContent.Find("m_togSupportItem_2").GetComponent<Toggle>();
            m_togSupportItem_3 = m_tfTacticalSupportContent.Find("m_togSupportItem_3").GetComponent<Toggle>();
            m_togSupportItem_4 = m_tfTacticalSupportContent.Find("m_togSupportItem_4").GetComponent<Toggle>();
            m_togArcLine = m_tfTacticalSupportContent.Find("m_togArcLine").GetComponent<Toggle>();
            m_togCmdType_1 = transform.Find("CmdTypeContent/m_togCmdType_1").GetComponent<Toggle>();
            m_togCmdType_2 = transform.Find("CmdTypeContent/m_togCmdType_2").GetComponent<Toggle>();
            m_togCmdType_3 = transform.Find("CmdTypeContent/m_togCmdType_3").GetComponent<Toggle>();
            m_togCmdType_4 = transform.Find("CmdTypeContent/m_togCmdType_4").GetComponent<Toggle>();
            m_imgOffTypeIcon_1 = m_togCmdType_1.transform.Find("m_imgOffTypeIcon_1").GetComponent<Image>();
            m_imgOffTypeIcon_2 = m_togCmdType_2.transform.Find("m_imgOffTypeIcon_2").GetComponent<Image>();
            m_imgOffTypeIcon_3 = m_togCmdType_3.transform.Find("m_imgOffTypeIcon_3").GetComponent<Image>();
            m_imgOffTypeIcon_4 = m_togCmdType_4.transform.Find("m_imgOffTypeIcon_4").GetComponent<Image>();
            m_tfCmdContent = transform.Find("m_tfCmdContent");
            m_btnSelectAll = transform.Find("Bottom/m_btnSelectAll").GetComponent<Button>();
            m_tfHeadContent = transform.Find("Bottom/m_tfHeadContent");
            _errorTipContent = transform.Find("m_tfErrorTipContent").transform;
            m_itemLeaderContent = transform.Find("TopLeft/m_itemLeaderContent").gameObject;
            m_textTimer = transform.Find("TopRight/m_textTimer").GetComponent<Text>();

            m_tfHpBarContent = transform.Find("m_tfHpBarContent");
            m_btnStartGame = transform.Find("m_btnStartGame").GetComponent<Button>();
            m_btnPrevStep = transform.Find("m_btnPrevStep").GetComponent<Button>();
            m_btnSupportRocker = transform.Find("m_btnSupportRocker").GetComponent<Button>();
            m_btnReturnLeaderView = transform.Find("m_btnReturnLeaderView").GetComponent<Button>();

            m_itemTargetSelect = transform.Find("m_itemTargetSelect").gameObject;

            m_tfShootMode = transform.Find("m_tfShootMode");
            m_tfBulletContent = m_tfShootMode.Find("m_tfBulletContent");
            m_btnFire = m_tfShootMode.Find("m_btnFire").GetComponent<Button>();
            m_btnFireSecond = m_tfShootMode.Find("m_btnFireSecond").GetComponent<Button>();
            m_imgAimIcon = m_tfShootMode.Find("m_imgAimIcon").GetComponent<Image>();
            m_btnReturnLeaderMode = m_tfShootMode.Find("m_btnReturnLeaderMode").GetComponent<Button>();
            m_btnFps = m_tfShootMode.Find("m_btnFps").GetComponent<Button>();

            m_tfFpsMode = transform.Find("m_tfFpsMode");
            m_btnExitAimState = m_tfFpsMode.Find("m_btnExitAimState").GetComponent<Button>();
            m_btnFireTps = m_tfFpsMode.Find("m_btnFireTps").GetComponent<Button>();
            m_imgTpsCrossHair = m_tfFpsMode.Find("m_imgTpsCrossHair").GetComponent<Image>();

            _targetSelect = CreateWidget<UIBattleTargetSelect>(m_itemTargetSelect, false);

            // 添加命令类型Toggle的事件监听
            m_togCmdType_1.onValueChanged.AddListener(OnToggleCmdType1Change);
            m_togCmdType_2.onValueChanged.AddListener(OnToggleCmdType2Change);
            m_togCmdType_3.onValueChanged.AddListener(OnToggleCmdType3Change);
            m_togCmdType_4.onValueChanged.AddListener(OnToggleCmdType4Change);

            m_togSupportItem_1.onValueChanged.AddListener(OnToggleSupportType1Change);
            m_togSupportItem_2.onValueChanged.AddListener(OnToggleSupportType2Change);
            m_togSupportItem_3.onValueChanged.AddListener(OnToggleSupportType3Change);
            m_togSupportItem_4.onValueChanged.AddListener(OnToggleSupportType4Change);

            m_btnChangeRoleSpeed.onClick.AddListener(OnClickChangeRoleSpeedBtn);
            m_btnClock.onClick.AddListener(OnClickClockBtn);
            m_btnAchi.onClick.AddListener(OnClickAchiBtn);
            m_btnSetting.onClick.AddListener(OnClickSettingBtn);
            m_btnTps.onClick.AddListener(OnClickTpsBtn);
            m_btnSelectAll.onClick.AddListener(OnClickSelectAllBtn);
            m_togTacticalSupport.onValueChanged.AddListener(OnTogglem_togTacticalSupportChange);
            m_togNightVision.onValueChanged.AddListener(OnTogglem_togNightVisionChange);
            m_togHotVision.onValueChanged.AddListener(OnTogglem_togHotVisionChange);
            m_btnStartGame.onClick.AddListener(OnClickStartGame);
            m_btnPrevStep.onClick.AddListener(OnPrevStepClick);
            m_btnReturnLeaderView.onClick.AddListener(OnClickReturnLeaderView);

            // TPS射击模式
            m_btnFire.onClick.AddListener(OnClickFire);
            m_btnFireSecond.onClick.AddListener(OnClickFireSecond);
            m_btnReturnLeaderMode.onClick.AddListener(OnClickReturnLeaderMode);
            m_btnFps.onClick.AddListener(OnClickFpsBtn);

            // FPS射击模式
            m_btnExitAimState.onClick.AddListener(OnClickExitAimState);
            m_btnFireTps.onClick.AddListener(OnClickFire);

            _leaderItem = CreateWidget<UIBattleLeaderItem>(m_itemLeaderContent);

            m_imgTpsCrossHair.gameObject.AddComponent<CrosshairColorUpdater>(); // 添加准星颜色更新组件
            m_imgAimIcon.gameObject.AddComponent<CrosshairColorUpdater>(); // 添加准星颜色更新组件
            UpdateSpeedLevelIndicator(m_speedGear); // 初始化时设置为巡航档
            UpdateTimeFlowIndicator(m_currentTimeFlowRate); // 初始化时设置为正常流速
            RenderRoleHeadItem(); // 渲染角色头像UI
            RenderBulletItem(); // 渲染炮弹UI
            InitLeaderData(); // 初始化领队数据
            RegisterTacticalSupportUI(); // 注册编队支持UI
            InitUIElements(); // 初始化UI元素 - 用于UI移动动画
            ViewUtils.RemoveAllChildren(m_tfHpBarContent);

            AddUIEvent<string>(GameCommon.ActorEventDefine.TestShowErrorTip, ShowErrorTip); // 错误提示
            AddUIEvent<UnitActor>(GameCommon.ActorEventDefine.RegisterHealthBarUI, RegisterHealthBarUI); // 注册角色血条
            AddUIEvent(GameCommon.ActorEventDefine.TestSetLeader, InitLeaderData); // 领队初始化完成
            AddUIEvent(GameCommon.ActorEventDefine.TestReleaseAoeSkill, AoeSkillRelease); // AOE技能释放
            AddUIEvent(GameCommon.ActorEventDefine.TestCancelAoeSkill, AoeSkillCancel); // 技能取消
            AddUIEvent(GameCommon.ActorEventDefine.BattleCmdContentRefresh, OnCmdExcCompleted); // 命令执行完成
            AddUIEvent<CommandType>(GameCommon.ActorEventDefine.TestSelectAttackTarget, UseAttackSkill);
            AddUIEvent(GameCommon.ActorEventDefine.TestRestoreUI, SelectTargetCompleted);
            AddUIEvent<uint>(GameCommon.ActorEventDefine.BattleEjectionHit, EjectionHitCount);

            StartTimer();
        }

        #endregion

        // 领队初始化 -- 游戏开始时和领队切换时调用
        private void InitLeaderData()
        {
            _leaderItem.SetData();
            _leaderUnit = BattleSystem.Instance.GetLeader();
        }

        #region 事件

        private GearLevel m_speedGear = GearLevel.Normal;

        private void OnClickChangeRoleSpeedBtn()
        {
            Debug.Log("正在切换角色速度");

            m_speedGear = m_speedGear switch
            {
                GearLevel.Stop => GearLevel.Low,
                GearLevel.Low => GearLevel.Normal,
                GearLevel.Normal => GearLevel.Fast,
                _ => GearLevel.Stop
            };

            // 更新速度等级指示器
            UpdateSpeedLevelIndicator(m_speedGear);
        }

        private void OnTogglem_togNightVisionChange(bool isOn)
        {
            if (m_togHotVision.isOn && isOn)
            {
                m_togHotVision.isOn = false;
            }

            BattleSystem.Instance.SpecialVision.NightVisionState(isOn);
            if (!isOn)
            {
                Debug.Log("关闭夜视");
                return;
            }

            Debug.Log($"开启夜视");
        }

        private void OnTogglem_togHotVisionChange(bool isOn)
        {
            if (m_togNightVision.isOn && isOn)
            {
                m_togNightVision.isOn = false;
            }

            BattleSystem.Instance.SpecialVision.ThermalVisionState(isOn);
            if (!isOn)
            {
                Debug.Log("关闭热成像");
                return;
            }

            Debug.Log("开启热成像");
        }

        private TimeFlowRate m_currentTimeFlowRate = TimeFlowRate.Normal;

        private void OnClickClockBtn()
        {
            m_currentTimeFlowRate = m_currentTimeFlowRate switch
            {
                TimeFlowRate.Stop => TimeFlowRate.Slow,
                TimeFlowRate.Slow => TimeFlowRate.Normal,
                _ => TimeFlowRate.Stop
            };
            Debug.Log("正在切换时间流速");
            UpdateTimeFlowIndicator(m_currentTimeFlowRate);
        }

        private void OnClickAchiBtn()
        {
            Debug.Log("正在查看成就");
        }

        private void OnClickSettingBtn()
        {
            Debug.Log("正在打开设置界面");
            var req = new Message.C_S_FightEnd();
            req.PacketHead = NetPacket.BuildPacketHead(UserDataModule.Instance.PlayerID);
            GameNet.Net.Send(req);
        }

        private void OnTogglem_togTacticalSupportChange(bool isOn)
        {
            if (isOn) m_togArcLine.isOn = true;
            m_tfTacticalSupportContent.gameObject.SetActive(isOn);

            if (!isOn)
            {
                Debug.Log("关闭战术支援");
                return;
            }

            Debug.Log("开启战术支援");
        }


        // 添加命令类型Toggle的事件处理方法
        private void OnToggleCmdType1Change(bool isOn)
        {
            m_imgOffTypeIcon_1.enabled = !isOn;
            if (!isOn)
            {
                if (m_currentCmdTypeName == "Formation")
                    m_tfCmdContent.gameObject.SetActive(false);
                return;
            }

            m_currentCmdTypeName = "Formation";

            // 关闭其他Toggle
            m_togCmdType_2.isOn = false;
            m_togCmdType_3.isOn = false;
            m_togCmdType_4.isOn = false;

            // 在这里添加命令类型1的处理逻辑
            RenderCmdContent();
        }

        private void OnToggleCmdType2Change(bool isOn)
        {
            m_imgOffTypeIcon_2.enabled = !isOn;
            if (!isOn)
            {
                if (m_currentCmdTypeName == "State")
                    m_tfCmdContent.gameObject.SetActive(false);
                return;
            }

            m_currentCmdTypeName = "State";

            // 关闭其他Toggle
            m_togCmdType_1.isOn = false;
            m_togCmdType_3.isOn = false;
            m_togCmdType_4.isOn = false;

            // 在这里添加命令类型2的处理逻辑
            RenderCmdContent();
        }

        private void OnToggleCmdType3Change(bool isOn)
        {
            m_imgOffTypeIcon_3.enabled = !isOn;
            if (!isOn)
            {
                if (m_currentCmdTypeName == "Behavior")
                    m_tfCmdContent.gameObject.SetActive(false);
                return;
            }

            m_currentCmdTypeName = "Behavior";

            // 关闭其他Toggle
            m_togCmdType_1.isOn = false;
            m_togCmdType_2.isOn = false;
            m_togCmdType_4.isOn = false;

            // 在这里添加命令类型3的处理逻辑
            RenderCmdContent();
        }

        private void OnToggleCmdType4Change(bool isOn)
        {
            m_imgOffTypeIcon_4.enabled = !isOn;
            if (!isOn)
            {
                if (m_currentCmdTypeName == "Attack")
                    m_tfCmdContent.gameObject.SetActive(false);
                return;
            }

            m_currentCmdTypeName = "Attack";

            // 关闭其他Toggle
            m_togCmdType_1.isOn = false;
            m_togCmdType_2.isOn = false;
            m_togCmdType_3.isOn = false;

            // 在这里添加命令类型4的处理逻辑
            RenderCmdContent();
        }

        private bool _isSelectAll;

        private void OnClickSelectAllBtn()
        {
            var toggles = m_tfHeadContent.GetComponentsInChildren<Toggle>();
            _isSelectAll = !_isSelectAll;
            foreach (var toggle in toggles)
            {
                toggle.isOn = _isSelectAll;
            }

            BattleSystem.Instance.SelectLeader(_isSelectAll);
        }

        // 火炮打击 -- 范围攻击
        private void OnToggleSupportType1Change(bool isOn)
        {
            if (!isOn) return;

            m_togTacticalSupport.isOn = false;
            PlayExitAnimation(MoveDistance, ShowPrevBtn);
            Debug.Log("正在使用火炮打击");
        }

        // 旋翼支援 -- 召唤直升机单位
        private void OnToggleSupportType2Change(bool isOn)
        {
            if (!isOn) return;

            m_togTacticalSupport.isOn = false;
            Debug.Log("正在使用旋翼支援");
        }

        // 制空支援 -- 空袭反制
        private void OnToggleSupportType3Change(bool isOn)
        {
            if (!isOn) return;

            m_togTacticalSupport.isOn = false;
            Debug.Log("正在使用制空支援");
        }

        // 战术轰炸 -- 范围轰炸
        private void OnToggleSupportType4Change(bool isOn)
        {
            if (!isOn) return;

            m_togTacticalSupport.isOn = false;
            PlayExitAnimation(MoveDistance, ShowPrevBtn);
            Debug.Log("正在使用战术轰炸");
        }

        private void OnClickStartGame()
        {
            Debug.Log("正在开始游戏");
            GameEvent.Send(GameCommon.ActorEventDefine.TestStartGame);
        }

        private void OnPrevStepClick()
        {
            m_btnPrevStep.gameObject.SetActive(false);

            m_btnSupportRocker.gameObject.SetActive(false);
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, false);
            RestoreUI();
        }

        private void OnClickReturnLeaderView()
        {
            Debug.Log("正在返回领队视角");
            m_btnReturnLeaderView.gameObject.SetActive(false);
            RestoreUI();
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, false);
            GameEvent.Send(GameCommon.ActorEventDefine.BattleCameraLock, false);
        }

        private void OnClickFire()
        {
            var camTrans = BattleSystem.Instance.MainCamera.transform;
            _leaderUnit.Controller.AttackBehavior.AttackInDirection(camTrans);
            Debug.Log("开火");
        }

        private void OnClickReturnLeaderMode()
        {
            m_tfShootMode.gameObject.SetActive(false);

            Bottom.gameObject.SetActive(true);
            CmdTypeContent.gameObject.SetActive(true);
            BottomRight.gameObject.SetActive(true);

            GameEvent.Send(GameCommon.ActorEventDefine.BattleSetCameraMode, BattleCameraMode.Commander);
            Debug.Log("返回指挥模式");
        }

        private void OnClickTpsBtn()
        {
            Bottom.gameObject.SetActive(false);
            CmdTypeContent.gameObject.SetActive(false);
            BottomRight.gameObject.SetActive(false);

            m_tfShootMode.gameObject.SetActive(true);
            GameEvent.Send(GameCommon.ActorEventDefine.BattleSetCameraMode, BattleCameraMode.Tps);
            Debug.Log("正在进入Tps模式");
        }

        private void OnClickFpsBtn()
        {
            m_tfFpsMode.gameObject.SetActive(true);
            m_tfShootMode.gameObject.SetActive(false);

            GameEvent.Send(GameCommon.ActorEventDefine.BattleSetCameraMode, BattleCameraMode.Fps);
            Debug.Log("切换至FPS模式");
        }

        private void OnClickFireSecond()
        {
            Debug.Log("副武器开火");
        }

        private void OnClickExitAimState()
        {
            m_tfFpsMode.gameObject.SetActive(false);
            m_tfShootMode.gameObject.SetActive(true);

            GameEvent.Send(GameCommon.ActorEventDefine.BattleSetCameraMode, BattleCameraMode.Tps);
            Debug.Log("退出瞄准模式");
        }

        #endregion

        /// <summary>
        /// 根据当前档位更新速度等级指示器
        /// </summary>
        /// <param name="currentGear">当前档位</param>
        private void UpdateSpeedLevelIndicator(GearLevel currentGear)
        {
            // 确保有子对象
            if (m_tfSpeedLvContent == null || m_tfSpeedLvContent.childCount < 4)
            {
                Debug.LogWarning("速度等级指示器未正确设置");
                return;
            }

            // 获取所有子对象的Toggle组件
            Toggle[] toggles = new Toggle[4];
            for (int i = 0; i < 4; i++)
            {
                toggles[i] = m_tfSpeedLvContent.GetChild(i).GetComponent<Toggle>();
                if (toggles[i] == null)
                {
                    Debug.LogWarning($"第{i + 1}个速度等级指示器没有Toggle组件");
                    return;
                }
            }

            var leader = BattleSystem.Instance.GetLeader();
            // 根据当前档位设置Toggle状态
            // 注意：索引0是最左侧/顶部的Toggle，索引3是最右侧/底部的Toggle
            switch (currentGear)
            {
                case GearLevel.Stop:
                    // 仅开启第4个Toggle
                    toggles[0].isOn = false;
                    toggles[1].isOn = false;
                    toggles[2].isOn = false;
                    toggles[3].isOn = true;
                    m_textSpeedLv.text = "停泊";
                    GameEvent.Send(GameCommon.ActorEventDefine.TestSetLeaderMoveState, LeaderMoveState.Stop);
                    SetRoleSpeed(leader, 0);
                    break;

                case GearLevel.Low:
                    // 开启第3和第4个Toggle
                    toggles[0].isOn = false;
                    toggles[1].isOn = false;
                    toggles[2].isOn = true;
                    toggles[3].isOn = true;
                    m_textSpeedLv.text = "低速";
                    GameEvent.Send(GameCommon.ActorEventDefine.TestSetLeaderMoveState, LeaderMoveState.NormalMove);
                    SetRoleSpeed(leader, 0.5f);
                    break;

                case GearLevel.Normal:
                    // 开启第2、3和第4个Toggle
                    toggles[0].isOn = false;
                    toggles[1].isOn = true;
                    toggles[2].isOn = true;
                    toggles[3].isOn = true;
                    m_textSpeedLv.text = "巡航";
                    SetRoleSpeed(leader, 1.2f);
                    break;

                case GearLevel.Fast:
                    // 全部开启
                    toggles[0].isOn = true;
                    toggles[1].isOn = true;
                    toggles[2].isOn = true;
                    toggles[3].isOn = true;
                    m_textSpeedLv.text = "极速";
                    SetRoleSpeed(leader, 2f);
                    break;

                default:
                    Debug.LogWarning($"未处理的档位类型: {currentGear}");
                    break;
            }
        }


        #region 命令相关

        private void OnCmdExcCompleted()
        {
            if (m_tfCmdContent.gameObject.activeSelf) RenderCmdContent();
        }

        private void RenderCmdContent()
        {
            var cmdType = m_currentCmdTypeName;
            var uuidSet = BattleSystem.Instance.SelectedRoles;
            var selectedUnits = BattleContext.GetAllUnits()
                .Where(x => uuidSet.Contains(x.Uuid)).ToList();
            ViewUtils.RemoveAllChildren(m_tfCmdContent);

            for (var i = 1; i <= 8; i++)
            {
                var (show, isOn) = CalcCommandState(cmdType, i, selectedUnits); // 新增函数
                var cmdItem = CreateWidgetByType<UIBattleCmdItem>(m_tfCmdContent);
                cmdItem.SetData(cmdType, i, show, isOn);
            }

            m_tfCmdContent.gameObject.SetActive(true);
        }

        private static (bool showIcon, bool? stateOn) CalcCommandState(string cmdType, int index, List<UnitActor> units)
        {
            if (units.Count == 0)
                return (false, null);
            if (IsToggleCommand(cmdType, index))
            {
                bool? commonState = null;
                foreach (var unit in units)
                {
                    bool state = CommandTypeComparer.IsCommandActive(unit, cmdType, index);
                    if (commonState == null)
                        commonState = state;
                    else if (commonState != state)
                        return (false, null);
                }

                return (true, commonState);
            }

            if (IsExclusiveGroupCommand(cmdType))
            {
                var expected = BattleConstants.GetCommandType(cmdType, index);

                foreach (var unit in units)
                {
                    var current = CommandTypeComparer.GetCurrentCommand(unit, cmdType, index);
                    if (current != expected)
                        return (false, null);
                }

                return (true, true); // 所有单位当前状态就是这个指令，才显示状态图标
            }

            return (false, null);
        }

        private static bool IsToggleCommand(string cmdType, int index)
        {
            return cmdType == "Attack" && (index == 4 || index == 5 || index == 6);
        }

        private static bool IsExclusiveGroupCommand(string cmdType)
        {
            return cmdType == "Formation" || cmdType == "State" || cmdType == "Behavior";
        }

        #endregion


        private void RenderRoleHeadItem()
        {
            ViewUtils.RemoveAllChildren(m_tfHeadContent);
            var roleList = BattleContext.GetAllUnits().Where(u => u.Camp == UnitCamp.Player && !u.IsLeader).ToList();

            foreach (var roleData in roleList)
            {
                var headItem = CreateWidgetByType<UIBattleHeadItem>(m_tfHeadContent);
                headItem.SetData(roleData.RoleId, roleData.Uuid, roleData.UnitAttribute.Hp, roleData.UnitAttribute.MaxHp);
            }
        }

        private void SetRoleSpeed(UnitActor unitActor, float speed)
        {
            if (speed == 0) return;
            unitActor.Controller.SetMoveSpeed(speed);
        }

        #region 错误提示

        private Transform _errorTipContent;

        private void ShowErrorTip(string tipText)
        {
            var tipDialog = CreateWidgetByType<UIBattleTipDialog>(_errorTipContent);
            tipDialog.SetData(tipText);
        }

        #endregion

        #region 计时器

        private float _elapsedTime;
        private bool _isTimerRunning;
        private Tween _timerTween;

        /// <summary>
        /// 根据当前时间流速更新指示器
        /// </summary>
        /// <param name="currentTimeFlow">当前时间流速</param>
        private void UpdateTimeFlowIndicator(TimeFlowRate currentTimeFlow)
        {
            // 确保有子对象
            if (m_tfTimeLvContent == null || m_tfTimeLvContent.childCount < 3)
            {
                Debug.LogWarning("时间流速指示器未正确设置");
                return;
            }

            // 获取所有子对象的Toggle组件
            Toggle[] toggles = new Toggle[3];
            for (int i = 0; i < 3; i++)
            {
                toggles[i] = m_tfTimeLvContent.GetChild(i).GetComponent<Toggle>();
                if (toggles[i] == null)
                {
                    Debug.LogWarning($"第{i + 1}个时间流速指示器没有Toggle组件");
                    return;
                }
            }

            // 根据当前时间流速设置Toggle状态
            // 注意：索引0是最左侧/顶部的Toggle，索引2是最右侧/底部的Toggle
            switch (currentTimeFlow)
            {
                case TimeFlowRate.Stop:
                    // 仅开启第1个Toggle
                    toggles[0].isOn = true;
                    toggles[1].isOn = false;
                    toggles[2].isOn = false;
                    SetTimerSpeed(0);
                    break;

                case TimeFlowRate.Slow:
                    // 开启第1和第2个Toggle
                    toggles[0].isOn = true;
                    toggles[1].isOn = true;
                    toggles[2].isOn = false;
                    SetTimerSpeed(0.5f);
                    break;

                case TimeFlowRate.Normal:
                    // 全部开启
                    toggles[0].isOn = true;
                    toggles[1].isOn = true;
                    toggles[2].isOn = true;
                    SetTimerSpeed(1);
                    break;

                default:
                    Debug.LogWarning($"未处理的时间流速类型: {currentTimeFlow}");
                    break;
            }
        }

        /// <summary>
        /// 启动计时器
        /// </summary>
        public void StartTimer()
        {
            if (_isTimerRunning) return;

            _elapsedTime = 0f;
            _isTimerRunning = true;

            _timerTween = DOTween.To(() => _elapsedTime, x => _elapsedTime = x, 99999f, 99999f)
                .SetEase(Ease.Linear)
                .OnUpdate(UpdateTimerText)
                .SetLink(gameObject);

            _timerTween.timeScale = 1; // 初始速度
        }

        private void SetTimerSpeed(float speed)
        {
            if (_timerTween == null) return;

            if (speed == 0f)
            {
                _timerTween.Pause();
            }
            else
            {
                if (!_timerTween.IsPlaying())
                    _timerTween.Play();
                _timerTween.timeScale = speed;
            }
        }

        /// <summary>
        /// 停止计时器
        /// </summary>
        public void StopTimer()
        {
            _isTimerRunning = false;
        }

        /// <summary>
        /// 更新计时器文本显示
        /// </summary>
        private void UpdateTimerText()
        {
            int seconds = (int)_elapsedTime % 60;
            int minutes = (int)_elapsedTime / 60;

            m_textTimer.text = $"{minutes:00}:{seconds:00}";
        }

        #endregion

        // 注册支援系统UI
        private void RegisterTacticalSupportUI()
        {
            var imageList = m_tfTacticalSupportContent.GetComponentsInChildren<Image>();
            foreach (var image in imageList)
            {
                if (image.name.StartsWith("m_togSupportItem") || image.name.StartsWith("Fous"))
                    image.alphaHitTestMinimumThreshold = 0.1f;
            }
        }

        private void RegisterHealthBarUI(UnitActor unitActor)
        {
            var healthBar = CreateWidgetByType<UIBattleUnitHealthBar>(m_tfHpBarContent);
            healthBar.SetData(unitActor.MainBody, unitActor.RoleName, unitActor.Uuid, unitActor.UnitAttribute.Hp, unitActor.UnitAttribute.MaxHp);
        }

        private void RenderBulletItem()
        {
            ViewUtils.RemoveAllChildren(m_tfBulletContent);
            var roleId = BattleSystem.Instance.GetLeader().RoleId;
            var weaponId = TableMgr.Instance.TableMecha.Root[roleId].PrimaryWeaponID;
            var shellIdList = TableMgr.Instance.TableWeapon.Root[weaponId].Shells;

            foreach (var shellId in shellIdList)
            {
                var bulletItem = CreateWidgetByType<UIBattleBulletItem>(m_tfBulletContent);
                bulletItem.SetData(shellId);
            }
        }

        #region UI移动动画

        private RectTransform Bottom;
        private RectTransform TopLeft;
        private RectTransform TopRight;
        private RectTransform BottomLeft;
        private RectTransform BottomRight;
        private RectTransform CmdTypeContent;

        private const float MoveDuration = 0.5f;
        private const float MoveDistance = 500f; // 足够把元素移出屏幕

        private void InitUIElements()
        {
            Bottom = transform.Find("Bottom").GetComponent<RectTransform>();
            TopLeft = transform.Find("TopLeft").GetComponent<RectTransform>();
            TopRight = transform.Find("TopRight").GetComponent<RectTransform>();
            BottomLeft = transform.Find("BottomLeft").GetComponent<RectTransform>();
            BottomRight = transform.Find("BottomRight").GetComponent<RectTransform>();
            CmdTypeContent = transform.Find("CmdTypeContent").GetComponent<RectTransform>();
        }

        private void PlayExitAnimation(float moveDistance = MoveDistance, Action onComplete = null)
        {
            var seq = DOTween.Sequence();
            seq.Append(Bottom.DOAnchorPosY(Bottom.anchoredPosition.y - moveDistance, MoveDuration));
            seq.Join(TopLeft.DOAnchorPos(TopLeft.anchoredPosition + new Vector2(-moveDistance, moveDistance), MoveDuration));
            seq.Join(TopRight.DOAnchorPos(TopRight.anchoredPosition + new Vector2(moveDistance, moveDistance), MoveDuration));
            // seq.Join(BottomLeft.DOAnchorPos(BottomLeft.anchoredPosition + new Vector2(-moveDistance, -moveDistance), MoveDuration));
            seq.Join(BottomRight.DOAnchorPos(BottomRight.anchoredPosition + new Vector2(moveDistance, -moveDistance), MoveDuration));
            seq.Join(CmdTypeContent.DOAnchorPosX(CmdTypeContent.anchoredPosition.x + moveDistance, MoveDuration));

            seq.SetEase(Ease.InBack);

            if (onComplete != null)
                seq.OnComplete(() => onComplete());
        }

        private void RestoreUI()
        {
            PlayExitAnimation(-MoveDistance);
        }

        #endregion

        // 显示上一步按钮
        private void ShowPrevBtn()
        {
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, true);

            m_btnSupportRocker.gameObject.SetActive(true);
            m_btnPrevStep.gameObject.SetActive(true);
        }

        private void AoeSkillRelease()
        {
            m_btnPrevStep.onClick.Invoke();
        }

        private void AoeSkillCancel()
        {
        }

        #region 目标选取

        // 单选敌方单位
        private void UseAttackSkill(CommandType cmdType)
        {
            if (m_togCmdType_4.isOn)
            {
                m_togCmdType_4.isOn = false;
                m_togCmdType_4.onValueChanged.Invoke(false);
            }

            var unitCount = BattleContext.GetAllUnits().Count(x => x.Camp == UnitCamp.Player);
            if (unitCount == 0)
            {
                ShowErrorTip("没有选中友方单位");
                return;
            }

            var context = new TargetSelectionContext
            {
                SourceCamp = UnitCamp.Player,
                RequiresConfirmation = true,
                Filter = new EnemyOnlySelection(UnitCamp.Player),
            };

            // 根据指令类型设置content属性值
            if (cmdType == CommandType.Ejection)
            {
                context.HintMessage = "请选择抛射地点";
                context.Filter = null;
                context.IsPositionSelection = true;

                context.OnPositionSelected = SetEjectionPosition;
            }
            else if (cmdType == CommandType.ShootTarget)
            {
                context.HintMessage = "请选择攻击目标";
                context.OnTargetsSelected = targets => SetAttackTarget(targets[0]);
            }
            else if (cmdType == CommandType.Bump)
            {
                context.HintMessage = "请选择冲撞攻击目标";
                context.OnTargetsSelected = targets => SetBumpTarget(targets[0]);
            }

            PlayExitAnimation(MoveDistance, () => OnTargetSelectMode(context));

            TargetSelectionSystem.Instance.BeginSelection(context);
        }

        private void SetEjectionPosition(Vector3 position)
        {
            Debug.Log($"抛射地点: {position}");
            var units = BattleContext.GetAllUnits().Where(x => BattleSystem.Instance.SelectedRoles.Contains(x.Uuid));

            _ejectionHitCount = 0;
            foreach (var unit in units)
            {
                _ejectionHitCount += 1;
                unit.EnqueueCommand(new Command(CommandType.Ejection, position));
            }

            m_itemTargetSelect.gameObject.SetActive(false);
            m_btnReturnLeaderView.gameObject.SetActive(true);
            GameEvent.Send(GameCommon.ActorEventDefine.BattleCameraLock, true);
            /*
             GameEvent.Send(GameCommon.ActorEventDefine.TestRestoreUI);
             GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, false);
            */
        }

        private int _ejectionHitCount;

        private void EjectionHitCount(uint uuid)
        {
            _ejectionHitCount -= 1;
            if (_ejectionHitCount == 0)
            {
                OnClickReturnLeaderView();
            }
        }

        private void SetBumpTarget(UnitActor targetUnit)
        {
            var units = BattleContext.GetAllUnits().Where(x => BattleSystem.Instance.SelectedRoles.Contains(x.Uuid));
            var bumpCommand = new RushCommand(
                targetUnit.MainBody,
                2f,
                100f,
                targetUnit.MainBody.Find("Extra_Collider").GetComponent<Collider>()
            );

            foreach (var unit in units)
            {
                var distance = Vector3.Distance(unit.MainBody.position, targetUnit.MainBody.position);
                if (distance < BattleConstants.BumpLimitDistance)
                {
                    ShowErrorTip($"角色{unit.RoleName}距离太近,无法进行冲撞攻击");
                    continue;
                }

                unit.Blackboard.Set(BlackboardKeys.HateTarget, targetUnit);
                unit.EnqueueCommand(new Command(CommandType.Bump, bumpCommand));
            }
        }

        private void OnTargetSelectMode(TargetSelectionContext context)
        {
            _targetSelect.Show(context);
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, true);
            // m_btnPrevStep.gameObject.SetActive(true);
        }

        private void SelectTargetCompleted()
        {
            RestoreUI();
            // m_btnPrevStep.gameObject.SetActive(false);
            m_itemTargetSelect.gameObject.SetActive(false);
        }

        private void SetAttackTarget(UnitActor target)
        {
            var units = BattleContext.GetAllUnits().Where(x => BattleSystem.Instance.SelectedRoles.Contains(x.Uuid));
            foreach (var unit in units)
            {
                unit.Blackboard.Set(BlackboardKeys.HateTarget, target);
            }

            _targetSelect.Hide();

            GameEvent.Send(GameCommon.ActorEventDefine.TestRestoreUI);
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, false);
        }

        // 多选友方单位
        private void UseHealSkill(UnitActor user)
        {
            TargetSelectionSystem.Instance.BeginSelection(new TargetSelectionContext
            {
                Filter = new AllyOnlySelection(user.Camp),
                SourceCamp = user.Camp,
                AllowMultiple = true,
                MaxSelectionCount = 3,
                RequiresConfirmation = true,
                OnTargetsSelected = targets => { Debug.Log($"治疗目标数量：{targets.Count}"); }
            });

            GameEvent.Send(GameCommon.ActorEventDefine.TestRestoreUI);
            GameEvent.Send(GameCommon.ActorEventDefine.TestTacticalSupportState, false);
        }

        #endregion
    }
}