using System.Collections.Generic;
using System.Linq;
using ChobiAssets.PTM;
using Cinemachine;
using Cysharp.Threading.Tasks;
using GameBase;
using Google.Protobuf.Collections;
using Message;
using TEngine;
using Unity.AI.Navigation;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Rendering;

namespace GameLogic
{
    public class BattleSystem : BehaviourSingleton<BattleSystem>
    {
        private GameObject _roomRoot;
        private BattleTickManager _battleTickManager;
        public GameObject MapRoot; // 地图根节点
        public PlayerInput PlayerInput;
        public Camera MainCamera;
        public int MapId;
        public SpecialVisionSwitch SpecialVision;

        private GameObject GameController;

        /// <summary>
        /// 异步加载战斗场景
        /// </summary>
        /// <param name="_level_id">关卡ID</param>
        /// <param name="_team_id">小队ID</param>
        public async UniTaskVoid LoadRoom(int _level_id, int _team_id, RepeatedField<SBattleUnit> _units)
        {
            await UniTask.Yield();
            Release(); // 释放资源
            GameNet.Net.AddNetworkEvent(this);
            NetPacket.RegisterPacket<S_C_FightEnd>();

            // 创建房间根对象
            MainCamera = CinemachineCore.Instance.GetActiveBrain(0).OutputCamera;
            _roomRoot = new GameObject("BattleRoom");

            // 加载地图
            LoadMap(_level_id);

            // 模块初始化
            ModuleInitialize(MapRoot.transform);

            _roomRoot.AddComponent<BehaviorTreeDebugger>(); // 行为树调试器
            _roomRoot.AddComponent<TargetingSystem>(); // 移动目标指示器
            _roomRoot.AddComponent<BattleLeaderMove>().Initialize(); // 领队移动组件
            _roomRoot.AddComponent<AoeTargetingManager>(); // Aoe技能目标指示器
            _roomRoot.AddComponent<TargetSelectionSystem>(); // 目标选取组件

            // 生成单位
            SpawnUnits(MapRoot.transform, _team_id, _units);

            // 帧管理器
            _battleTickManager = _roomRoot.AddComponent<BattleTickManager>();
            _battleTickManager.Initialize(this);

            // 显示战斗主界面
            GameModule.UI.ShowUIAsync<UIBattleMainPage>();

            GameEvent.Send(GameCommon.ActorEventDefine.TestInitInputAction);
        }

        /// <summary>
        /// 游戏主逻辑循环
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        public void Tick(float deltaTime)
        {
            PerceptionManager.Tick(deltaTime);
            BattleFormationController.Tick(deltaTime);

            foreach (var unit in BattleContext.GetAllUnits())
            {
                if (!IsBehaviorTreePaused || StepOneFrame)
                {
                    if (unit.DamageControlCenter.isDead) continue;
                    unit.Tick(deltaTime);
                }
            }

            StepOneFrame = false; // 重置
        }

        #region 地图与单位加载

        /// <summary>
        /// 加载地图与地图数据
        /// </summary>
        /// <param name="_level_id"></param>
        private void LoadMap(int _level_id)
        {
            // 加载地图  -- 临时使用
            var mapData = TableMgr.Instance.TableLevels.Root[_level_id];
            var mapId = mapData.UseMapId;
            MapId = mapId = 107;
            MapRoot = GameModule.Resource.LoadGameObject($"map_{mapId}");
            MapRoot.transform.SetParent(_roomRoot.transform);

            // 空袭控制器
            GameObject asController = new GameObject("AirStrikeController");
            asController.transform.SetParent(GameObject.Find("GameController").transform);
            Event_Controller_CS asEvent = asController.AddComponent<Event_Controller_CS>();
            asEvent.Trigger_Type = 0;
            asEvent.Trigger_Time = mapData.AirStrikeTime;
            asEvent.Event_Loop = 0;
            asEvent.Event_Type = 7;
            asEvent.AirStrike_Script = GameObject.Find("GameController/AirStrikeEvent").GetComponent<Air_Strike_Controller_CS>();

            var navMeshSurface = MapRoot.GetComponentInChildren<NavMeshSurface>();
            // 检查是否存在网格数据
            if (navMeshSurface.navMeshData == null)
            {
                Debug.LogError("找不到NavMeshSurface");
                navMeshSurface.navMeshData = GameModule.Resource.LoadAsset<UnityEngine.AI.NavMeshData>("NavMesh-map_" + _level_id);
            }

            if (navMeshSurface.navMeshData == null)
            {
                GameModule.UI.ShowUIAsync<DialogTipsText>("地图信息错误，请重新进入战斗");
                ViewUtils.EnterHall();
                return;
            }

            LoadLightSetting($"map_{mapId}LightingSettingsPreset");
        }

        private void SpawnUnits(Transform mapTf, int _team_id, RepeatedField<SBattleUnit> _units)
        {
            var playerContent = mapTf.Find("KeyPoint/PlayerPoint");
            var enemyContent = mapTf.Find("KeyPoint/EnemyPoint1");

            var unitIndex = 1;
            var playerIndex = 0;
            var enemyIndex = 0;

            foreach (var item in _units)
            {
                if (item.UnitType == 1)
                {
                    var playerTransform = playerContent.GetChild(playerIndex);
                    // ToDo: 临时使用 -- 指定玩家单位生成
                    var unit = GameModule.Resource.LoadGameObject($"role_10007001");
                    unit.transform.position = playerTransform.position;
                    unit.name = "player_" + unitIndex;
                    unit.tag = "Player";
                    SetupUnit(unit, item, playerIndex, UserDataModule.Instance.Roles[item.RoleId].TeamInfo.Captain);
                    unitIndex++;
                    playerIndex++;
                }
                else
                {
                    var trans = enemyContent.GetChild(enemyIndex);
                    var unit = GameModule.Resource.LoadGameObject($"role_{item.RoleId}001");
                    unit.transform.position = trans.position;
                    unit.name = $"Enemy_{enemyIndex}";
                    unit.tag = "Enemy";
                    SetupUnit(unit, item, item.RoleId);
                    enemyIndex++;
                }
            }

            var leader = BattleContext.GetAllUnits().FirstOrDefault(u => u.Camp == UnitCamp.Player && u.IsLeader);
            if (leader == null)
                return;
            // Debug.Log($"摄像头对齐到领队");
            GameEvent.Send(GameCommon.ActorEventDefine.TestChangeCameraLookAt, leader.MainBody);
            GameEvent.Send(GameCommon.ActorEventDefine.TestSetLeader);
        }

        private void SetupUnit(GameObject unit, SBattleUnit battleUnit, int index = 0, int leaderFlag = 0)
        {
            var unitComponent = unit.GetOrAddComponent<UnitActor>();

            var camp = battleUnit.UnitType == 1 ? UnitCamp.Player : UnitCamp.Enemy;
            unitComponent.Initialize(camp, battleUnit.RoleId, battleUnit.UUID, leaderFlag == 1, index);

            // 血条UI
            GameEvent.Send(GameCommon.ActorEventDefine.RegisterHealthBarUI, unitComponent);

            BattleContext.Register(unit.GetComponent<UnitActor>());
        }

        #endregion

        #region 网络模块

        /// <summary>
        ///  发送战斗动作
        /// </summary>
        /// <param name="actionType">1.移动 2.攻击 3.道具 10.我方死亡 11.敌方死亡</param>
        /// <param name="actionValue"></param>
        /// <param name="position">移动时表示目标位置,攻击时表示方向</param>
        private void SendFightAction(int actionType, uint actionValue, Vector3 position = default)
        {
            var action = new C_S_FightAction
            {
                PacketHead = NetPacket.BuildPacketHead(UserDataModule.Instance.PlayerID),
                ActionType = (uint)actionType,
                ActionValue = actionValue,
                Vec3 = new Point3F
                { X = position.x, Y = position.y, Z = position.z }
            };
            Debug.Log($"发送战斗动作 {actionType} {actionValue} {position}");
            // GameNet.Net.Send(action);
        }

        #endregion

        #region 销毁自身

        /// <summary>
        /// 结束战斗消息
        /// </summary>
        /// <param name="resp"></param>
        public void S_C_FightEnd(S_C_FightEnd resp)
        {
            this.DestroyGame();
        }

        /// <summary>
        /// 推出游戏回到大厅
        /// </summary>
        public void DestroyGame()
        {
            GameModule.UI.ShowUIAsync<UILoading>(5f);
            BattleContext.Clear();

            NetPacket.UnRegisterPacket<S_C_FightEnd>();
            GameNet.Net.RemoveNetworkEvent(this);

            Release();

            ViewUtils.EnterHall();
            ResetInstance();
        }

        public override void Destroy()
        {
            base.Destroy();
            GameNet.Net.RemoveNetworkEvent(this);
            Debug.Log("战斗场景实例被销毁");
        }

        #endregion

        #region GameManager

        public BattleFormationController BattleFormationController;
        public PerceptionManager PerceptionManager { get; private set; }

        public Transform GoalPoint;
        public Transform StartPoint;
        public BattleCameraManager BattleCameraManager;

        public void ModuleInitialize(Transform roomRoot)
        {
            var enemyPoint = roomRoot.Find("KeyPoint/EnemyPoint1");
            GoalPoint = enemyPoint.GetChild(enemyPoint.childCount - 1).transform;
            StartPoint = roomRoot.Find("KeyPoint/PlayerPoint").GetChild(0).transform;
            BattleFormationController = new BattleFormationController();
            PerceptionManager = new PerceptionManager();

            CoverPointManager.Init(); // 初始化掩体管理器

            var cameraContent = GameObject.Find("CameraContent").transform;
            BattleCameraManager = new BattleCameraManager(cameraContent);
            BattleCameraManager.Initialize();

            PlayerInput = cameraContent.GetComponent<PlayerInput>();
            GameController = GameObject.Find("GameController");

            SpecialVision = GameController.GetComponent<SpecialVisionSwitch>();

            GameEvent.AddEventListener<CommandType>(GameCommon.ActorEventDefine.UIBattleSendingInstruction, SetUnitCommand);
            GameEvent.AddEventListener<uint, Vector3>(GameCommon.ActorEventDefine.UIBattleSelectMovePoint, SetUnitMoveTarget);
            GameEvent.AddEventListener<uint, bool>(GameCommon.ActorEventDefine.UIBattleCheckCurrRole, ToggleRoleSelection);
            GameEvent.AddEventListener(GameCommon.ActorEventDefine.TestChangeAIEnableState, OpenOrCloseBehaviorTree);
            GameEvent.AddEventListener<int, uint, Vector3>(GameCommon.ActorEventDefine.PlayerAction, SendFightAction);
            GameEvent.AddEventListener(GameCommon.ActorEventDefine.BattleLeaderDead, OnBattleLeaderDead);
            GameEvent.AddEventListener<Transform>(GameCommon.ActorEventDefine.BattleArtillery, OnBattleArtillery);
        }

        private void OnBattleLeaderDead()
        {
            var newLeader = BattleContext.GetAllUnits().FirstOrDefault(u => u.Camp == UnitCamp.Player);
            if (newLeader == null) return;

            newLeader.IsLeader = true;
            newLeader.AI.StopAI();
            Debug.Log($"摄像头对齐到领队");
            GameEvent.Send(GameCommon.ActorEventDefine.TestChangeCameraLookAt, newLeader.MainBody);
            GameEvent.Send(GameCommon.ActorEventDefine.TestSetLeader);
        }

        public bool IsBehaviorTreePaused = false;
        public bool StepOneFrame = false;

        private void SetUnitMoveTarget(uint uuid, Vector3 targetPosition)
        {
            var unit = BattleContext.GetUnitByUuid(uuid);
            if (unit == null) return;

            unit.Blackboard.Set(BlackboardKeys.IsCommandInterrupt, true);
            unit.Blackboard.Set(BlackboardKeys.ExecutingCommand, CommandType.AIControl);
            unit.Blackboard.Set(BlackboardKeys.MoveTargetPosition, targetPosition);
            unit.Blackboard.Set(BlackboardKeys.CurrentFormation, FormationType.Empty);
        }


        public HashSet<uint> SelectedRoles = new();

        /// <summary>
        /// 切换角色选中状态
        /// 如果角色已选中则取消选中，否则加入选中列表
        /// </summary>
        private void ToggleRoleSelection(uint uuid, bool isSelect)
        {
            if (isSelect)
            {
                SelectedRoles.Add(uuid);
                Debug.Log($"选中角色：{uuid}");
            }
            else
            {
                SelectedRoles.Remove(uuid);
                Debug.Log($"取消选中角色：{uuid}");
            }

            GameEvent.Send(GameCommon.ActorEventDefine.BattleCmdContentRefresh);
        }

        // 切换为手动模式 -- 测试函数
        private void OpenOrCloseBehaviorTree()
        {
            foreach (var unit in BattleContext.GetAllUnits())
            {
                if (unit.Camp == UnitCamp.Enemy) continue;
                var isUseAI = unit.Blackboard.GetOrDefault(BlackboardKeys.IsUseAI);

                unit.Controller.StopMovement();
                unit.Blackboard.Set(BlackboardKeys.IsUseAI, !isUseAI);
            }
        }

        public void SelectLeader(bool isSelect)
        {
            var leader = GetLeader();
            if (leader == null) return;
            if (isSelect)
            {
                Debug.Log($"选中领队：{leader.Uuid}");
            }
            else
            {
                Debug.Log($"取消选中领队：{leader.Uuid}");
            }

            ToggleRoleSelection(leader.Uuid, isSelect);
        }

        public UnitActor GetLeader()
        {
            var leader = BattleContext.GetAllUnits().FirstOrDefault(u => u.Camp == UnitCamp.Player && u.IsLeader);
            if (leader != null)
                return leader;
            else
                return BattleContext.GetAllUnits().First();
        }

        public void Release()
        {
            if (_roomRoot != null)
                GameObject.Destroy(_roomRoot);

            BattleContext.Clear();
            BattleFormationController = null;
            PerceptionManager?.Release();
            PerceptionManager = null;
            GoalPoint = null;
            StartPoint = null;
            BattleCameraManager?.Release();
            CoverPointManager.ClearAll();

            GameEvent.RemoveEventListener<CommandType>(GameCommon.ActorEventDefine.UIBattleSendingInstruction, SetUnitCommand);
            GameEvent.RemoveEventListener<uint, Vector3>(GameCommon.ActorEventDefine.UIBattleSelectMovePoint, SetUnitMoveTarget);
            GameEvent.RemoveEventListener<uint, bool>(GameCommon.ActorEventDefine.UIBattleCheckCurrRole, ToggleRoleSelection);
            GameEvent.RemoveEventListener(GameCommon.ActorEventDefine.TestChangeAIEnableState, OpenOrCloseBehaviorTree);
            GameEvent.RemoveEventListener<int, uint, Vector3>(GameCommon.ActorEventDefine.PlayerAction, SendFightAction);
            GameEvent.RemoveEventListener(GameCommon.ActorEventDefine.BattleLeaderDead, OnBattleLeaderDead);
            GameEvent.RemoveEventListener<Transform>(GameCommon.ActorEventDefine.BattleArtillery, OnBattleArtillery);
        }

        #endregion

        #region 加载光照设置

        /// <summary>
        /// 加载光照设置
        /// </summary>
        /// <param name="path"></param>
        private static void LoadLightSetting(string path = "Map_107LightingSettingsPreset")
        {
            string json = GameModule.Resource.LoadAsset<TextAsset>(path).text;
            var snapshot = JsonUtility.FromJson<RenderSettingsSnapshot>(json);

            RenderSettings.ambientLight = snapshot.ambientLight;
            RenderSettings.ambientMode = snapshot.ambientMode;
            RenderSettings.ambientIntensity = snapshot.ambientIntensity;
            // RenderSettings.skybox = snapshot.skybox;
            RenderSettings.ambientSkyColor = snapshot.skyColor;
            RenderSettings.ambientEquatorColor = snapshot.equatorColor;
            RenderSettings.ambientGroundColor = snapshot.groundColor;
            RenderSettings.defaultReflectionMode = snapshot.reflectionMode;
            RenderSettings.defaultReflectionResolution = snapshot.reflectionResolution;
            RenderSettings.reflectionIntensity = snapshot.reflectionIntensity;
            RenderSettings.reflectionBounces = snapshot.reflectionBounces;
        }

        #endregion

        /// <summary>
        /// 指令处理
        /// </summary>
        /// <param name="commandType"></param>
        private void SetUnitCommand(CommandType commandType)
        {
            if (SelectedRoles.Count == 0)
            {
                GameEvent.Send(GameCommon.ActorEventDefine.TestShowErrorTip, "请选择至少一个角色");
                return;
            }

            var unitList = BattleContext.GetAllUnits().Where(u => SelectedRoles.Contains(u.Uuid)).ToList();

            if (BattleConstants.TeamCmdHashSet.Contains(commandType))
            {
                var alivePlayerCount = BattleContext.GetAllUnits().Count(x => x.Camp == UnitCamp.Player);
                if (unitList.Count < alivePlayerCount - 1)
                {
                    GameEvent.Send(GameCommon.ActorEventDefine.TestShowErrorTip, "当前指令为团体指令,需要指定全员");
                    return;
                }

                TeamCommandExecutor.Execute(commandType, unitList);
            }
            else if (BattleConstants.FormationCmdHashSet.Contains(commandType))
            {
                var alivePlayerCount = BattleContext.GetAllUnits().Count(x => x.Camp == UnitCamp.Player);
                if (unitList.Count < alivePlayerCount)
                {
                    GameEvent.Send(GameCommon.ActorEventDefine.TestShowErrorTip, "当前指令为团体指令,需要指定全员");
                    return;
                }

                var formationType = commandType switch
                {
                    CommandType.FormationDefault => FormationType.Default,
                    CommandType.FormationDisturb => FormationType.Disturb,
                    CommandType.FormationShieldWall => FormationType.ShieldWall,
                    CommandType.FormationLine => FormationType.Line,
                    CommandType.FormationRing => FormationType.Ring,
                    CommandType.FormationHourglass => FormationType.Hourglass,
                    CommandType.FormationWedge => FormationType.Wedge,
                    CommandType.FormationTwoByTwo => FormationType.TwoByTwo,
                    _ => FormationType.Default
                };

                BattleFormationController.StartFormationChange(formationType);
                return;
            }
            else if (BattleConstants.PostureCmdHashSet.Contains(commandType))
            {
                var posture = CommandTypeComparer.ConvertToFormationType(commandType);
                foreach (var unit in unitList)
                {
                    unit.EnqueueCommand(new Command(CommandType.PostureChange, posture));
                }

                return;
            }
            else if (BattleConstants.GearCmdHashSet.Contains(commandType))
            {
                var gearLevel = CommandTypeComparer.ConvertToGearLevel(commandType);
                foreach (var unit in unitList)
                {
                    unit.EnqueueCommand(new Command(CommandType.GearChange, gearLevel));
                }

                return;
            }
            else if (commandType == CommandType.AutoSkill)
            {
                var allEnabled = unitList.All(unit => unit.Blackboard.GetOrDefault(BlackboardKeys.AutoSkill));

                foreach (var unit in unitList)
                {
                    unit.Blackboard.Set(BlackboardKeys.AutoSkill, !allEnabled);
                }

                GameEvent.Send(GameCommon.ActorEventDefine.BattleCmdContentRefresh);
                return;
            }
            else if (commandType == CommandType.AutoFire)
            {
                var autoFire = unitList.All(unit => unit.Blackboard.GetOrDefault(BlackboardKeys.AutoAttack));
                foreach (var unit in unitList)
                {
                    unit.Blackboard.Set(BlackboardKeys.AutoAttack, !autoFire);
                }

                GameEvent.Send(GameCommon.ActorEventDefine.BattleCmdContentRefresh);

                return;
            }
            else if (BattleConstants.HideCmdContentHashSet.Contains(commandType))
            {
                GameEvent.Send(GameCommon.ActorEventDefine.TestSelectAttackTarget, commandType);
                return;
            }


            foreach (var unit in unitList)
            {
                unit.Blackboard.Set(BlackboardKeys.IsCommandInterrupt, true);
                unit.Blackboard.Set(BlackboardKeys.ExecutingCommand, commandType);

                unit.Blackboard.Set(BlackboardKeys.CurrentBehavior, commandType);
            }

            // 特殊指令处理 -- 需要切换摄像头模式
            /*if (commandType == CommandType.SelectAttackTarget)
            {
                var targetUnit = unitList[0].MainBody;
                GameEvent.Send(GameCommon.ActorEventDefine.TestChangeCameraLookAt, targetUnit);
            }*/
        }

        private void OnBattleArtillery(Transform targetTransform)
        {
            GameController.GetComponent<Artillery_Fire_CS>().Fire(targetTransform, 12);
        }
    }


    [System.Serializable]
    public class RenderSettingsSnapshot
    {
        public Color ambientLight;
        public AmbientMode ambientMode;
        public float ambientIntensity;
        public Material skybox;
        public Color skyColor;
        public Color equatorColor;
        public Color groundColor;
        public DefaultReflectionMode reflectionMode;
        public int reflectionResolution;
        public float reflectionIntensity;
        public int reflectionBounces;
    }
}