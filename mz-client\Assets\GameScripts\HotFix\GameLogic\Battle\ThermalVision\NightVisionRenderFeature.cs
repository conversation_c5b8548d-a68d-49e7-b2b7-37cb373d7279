﻿using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace GameLogic
{
    public class NightVisionRenderFeature : ScriptableRendererFeature
    {
        [System.Serializable]
        public class Settings
        {
            public Material nightVisionMaterial = null;
            public bool isNightVisionOn = true;
        }

        class NightVisionRenderPass : ScriptableRenderPass
        {
            private Material material;
            private RenderTargetHandle tempTexture;
            private bool isEnabled;

            public NightVisionRenderPass()
            {
                renderPassEvent = RenderPassEvent.AfterRenderingTransparents;
                tempTexture.Init("_NightVisionTempTex");
            }

            public void Setup(Material mat, bool enabled)
            {
                material = mat;
                isEnabled = enabled;
            }

            public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
            {
                // 分配临时RT
                cmd.GetTemporaryRT(tempTexture.id, cameraTextureDescriptor, FilterMode.Bilinear);
                ConfigureTarget(tempTexture.Identifier());
                ConfigureClear(ClearFlag.None, Color.clear);
            }

            public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
            {
                if (!isEnabled || material == null)
                    return;

                CommandBuffer cmd = CommandBufferPool.Get("NightVisionPass");

                // 新的cameraColorTargetHandle，类型是 RTHandle
                RTHandle source = renderingData.cameraData.renderer.cameraColorTargetHandle;

                // 使用配置的RenderTextureDescriptor
                var descriptor = renderingData.cameraData.cameraTargetDescriptor;
                descriptor.depthBufferBits = 0;

                // 申请临时RT，使用RenderTargetHandle
                cmd.GetTemporaryRT(tempTexture.id, descriptor, FilterMode.Bilinear);

                // Blit from source to temp
                cmd.Blit(source, tempTexture.Identifier(), material);

                // Blit back from temp to source
                cmd.Blit(tempTexture.Identifier(), source);

                context.ExecuteCommandBuffer(cmd);
                CommandBufferPool.Release(cmd);
            }


            public override void FrameCleanup(CommandBuffer cmd)
            {
                if (tempTexture != RenderTargetHandle.CameraTarget)
                {
                    cmd.ReleaseTemporaryRT(tempTexture.id);
                }
            }
        }

        public Settings settings = new Settings();
        private NightVisionRenderPass pass;

        public override void Create()
        {
            pass = new NightVisionRenderPass();
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            if (settings.nightVisionMaterial != null)
            {
                pass.Setup(settings.nightVisionMaterial, settings.isNightVisionOn);
                renderer.EnqueuePass(pass);
            }
        }
    }
}