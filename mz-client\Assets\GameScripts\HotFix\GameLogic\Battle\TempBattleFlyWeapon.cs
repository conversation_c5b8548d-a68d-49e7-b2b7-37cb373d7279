using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GameCommon;
using UnityEngine;

namespace GameLogic
{
    public class TempBattleFlyWeapon : BattleWeaponBase
    {
        public GameObject AP_Bullet_Prefab; // 子弹

        private float _fireCooldown;

        public override void Init()
        {
            Owner = this.transform.root.gameObject;
            base.Init();

            _fireCooldown = Fire_Rate;
            Owner = this.transform.parent.parent.gameObject;
            // 根据子弹类型设置不同初始速度
            // switch (currentBulletType)
            // {
            //     case 0: // AP
            // Current_Bullet_Velocity = Initial_Velocity_AP;
            //         break;
            //     case 1: // HE
            //         Current_Bullet_Velocity = Initial_Velocity_HE;
            //         break;
            // }
        }

        private void Update()
        {
            // 自动射击 -- 暂时关闭
            // AutoShoot();
            // 射击冷却 冷却时间是射速的倒数
            if (_fireCooldown > 0)
            {
                _fireCooldown -= Time.deltaTime;
            }
        }


        private IEnumerator Generate_Bullet()
        {
            // 测试代码
            var bulletObject = Instantiate(AP_Bullet_Prefab, this.transform.position - (this.transform.forward * Offset), this.transform.rotation) as GameObject;

            var dcc = Owner.GetComponent<Damage_Control_Center>();
            var _bullet_Velocity = dcc.Attr.GetCurWeaponsAttr().GetCurShellAttr().StartingSpeed;

            // Set values of "Bullet_Control_CS" in the bullet.
            Battle_Shell_Control bulletScript = bulletObject.GetComponent<Battle_Shell_Control>();
            bulletScript.Initial_Velocity = _bullet_Velocity;
            bulletScript.Life_Time = Life_Time;
            bulletScript.Attr = dcc.Attr.GetCurWeaponsAttr().GetCurShellAttr();

            // Set the tag.
            bulletObject.tag = "Finish"; // (Note.) The ray cast for aiming does not hit any object with "Finish" tag.

            // Set the layer.
            bulletObject.layer = BattleColliderLayer.Bullet;

            // Shoot.
            yield return new WaitForFixedUpdate();
            Rigidbody rigidbody = bulletObject.GetComponent<Rigidbody>();
            Vector3 currentVelocity = -bulletObject.transform.forward * _bullet_Velocity;
            // Vector3 currentVelocity = -bulletObject.transform.forward * 20f;
            rigidbody.velocity = currentVelocity;
        }

        #region 新增函数

        private void AutoShoot()
        {
            if (!currentTarget) return;

            var direction = Quaternion.LookRotation(currentTarget.transform.position - this.transform.position);
            this.transform.rotation = direction;
            // Debug.DrawLine(this.transform.position, currentTarget.transform.position, Color.red);
            CurrentFireRateToShoot += Time.deltaTime;
            if (CurrentFireRateToShoot >= Fire_Rate)
            {
                CurrentFireRateToShoot -= Fire_Rate;
                StartCoroutine(nameof(Generate_Bullet));
            }
        }

        public void FireFromCameraDirection(Transform cameraTransform, float maxDistance = 300f)
        {
            var origin = cameraTransform.position;
            var camForward = cameraTransform.forward;
            Vector3 result;

            if (Physics.Raycast(origin, camForward, out var hit, maxDistance))
            {
                result = hit.point; // 命中场景，返回命中点
            }
            else
            {
                // 没有命中，返回摄像机正前方 maxDistance 远的位置
                result = origin + camForward * maxDistance;
            }

            var direction = result - transform.position;
            TryFireTowardsDirection(direction);
        }


        /// <summary>
        /// 尝试从当前位置朝给定方向射击（受射速限制）
        /// </summary>
        private void TryFireTowardsDirection(Vector3 direction)
        {
            if (_fireCooldown > 0f) return;

            if (direction == Vector3.zero) return;

            // 调整朝向
            Quaternion lookRotation = Quaternion.LookRotation(-direction.normalized);
            transform.rotation = lookRotation;

            // 设置冷却
            _fireCooldown = Fire_Rate;

            // 立即开火
            StartCoroutine(Generate_Bullet());
        }


        /// <summary>
        /// 向指定目标点开火（受射速限制）
        /// </summary>
        public void TryFireAtTargetPosition(Vector3 targetPosition)
        {
            Vector3 direction = targetPosition - transform.position;
            TryFireTowardsDirection(direction);
        }

        /// <summary>
        /// 向指定目标点抛射（受射速限制）
        /// </summary>
        /// <param name="targetPosition"></param>
        /// <param name="launchAngle"></param>
        /// <param name="shellUuid"></param>
        public void FireProjectileAtTarget(Vector3 targetPosition, uint shellUuid, float launchAngle = 45f)
        {
            StartCoroutine(Generate_Projectile_Trajectory(targetPosition, shellUuid, launchAngle));
        }

        private IEnumerator Generate_Projectile_Trajectory(Vector3 targetPos, uint shellUuid, float launchAngleDeg)
        {
            GameObject bulletObject = Instantiate(AP_Bullet_Prefab, this.transform.position, Quaternion.identity);

            var dcc = Owner.GetComponent<Damage_Control_Center>();
            var _bullet_Velocity = dcc.Attr.GetCurWeaponsAttr().GetCurShellAttr().StartingSpeed;

            Battle_Shell_Control bulletScript = bulletObject.GetComponent<Battle_Shell_Control>();
            bulletScript.isEjectionMode = true;
            bulletScript.shellUuid = shellUuid;
            bulletScript.Initial_Velocity = _bullet_Velocity;
            bulletScript.Life_Time = Life_Time;
            bulletScript.Attr = dcc.Attr.GetCurWeaponsAttr().GetCurShellAttr();

            bulletObject.tag = "Finish";
            bulletObject.layer = BattleColliderLayer.Bullet;

            yield return new WaitForFixedUpdate();

            var rigidbody = bulletObject.GetComponent<Rigidbody>();

            // 获取抛射速度向量
            Vector3 velocity;
            bool valid = TryCalculateBallisticVelocity(targetPos, transform.position, launchAngleDeg, out velocity);
            if (!valid)
            {
                Debug.LogWarning("抛射计算失败，目标过远或过近");
                Destroy(bulletObject);
                yield break;
            }

            // 设置朝向
            transform.rotation = Quaternion.LookRotation(-velocity.normalized);

            // 设置速度
            rigidbody.velocity = velocity;
            bulletScript.isFlying = true;
        }

        /// <summary>
        /// 根据起点、终点和角度计算抛射初速度（忽略空气阻力）
        /// </summary>
        private bool TryCalculateBallisticVelocity(Vector3 target, Vector3 origin, float angleDeg, out Vector3 velocity)
        {
            velocity = Vector3.zero;

            var gravity = Mathf.Abs(Physics.gravity.y);
            var angleRad = angleDeg * Mathf.Deg2Rad;

            var planarTarget = new Vector3(target.x, 0f, target.z);
            var planarOrigin = new Vector3(origin.x, 0f, origin.z);

            var distance = Vector3.Distance(planarTarget, planarOrigin);
            var yOffset = target.y - origin.y;

            var cosAngle = Mathf.Cos(angleRad);
            var sinAngle = Mathf.Sin(angleRad);

            var denominator = 2f * (cosAngle * cosAngle) * (distance * Mathf.Tan(angleRad) - yOffset);

            if (denominator <= 0.001f)
                return false; // 无解或目标太近

            var speedSqr = (gravity * distance * distance) / denominator;

            if (speedSqr <= 0f)
                return false;

            var speed = Mathf.Sqrt(speedSqr);

            // 方向
            var direction = (planarTarget - planarOrigin).normalized;

            velocity = direction * speed * cosAngle + Vector3.up * speed * sinAngle;
            return true;
        }

        #endregion
    }
}