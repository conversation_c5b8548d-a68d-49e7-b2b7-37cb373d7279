﻿namespace GameLogic
{
    using UnityEngine;
    using UnityEngine.UI;

    public class CrosshairColorUpdater : MonoBehaviour
    {
        private Image _crosshairImage;
        public float maxDetectDistance = 200f;
        public LayerMask raycastMask = Physics.DefaultRaycastLayers;

        private enum CrosshairState
        {
            Enemy,
            Neutral
        }

        private CrosshairState _lastState = CrosshairState.Neutral;

        private static Color enemyColor = Color.red;
        private static Color normalColor = Color.white;
        private Camera _mainCamera;

        private void Start()
        {
            _crosshairImage = GetComponent<Image>();
            _mainCamera = BattleSystem.Instance.MainCamera;
        }

        private bool _isAimingEnemy;

        private void Update()
        {
            if (_mainCamera == null || _crosshairImage == null) return;

            Ray ray = new Ray(_mainCamera.transform.position, _mainCamera.transform.forward);

            _isAimingEnemy = false;

            if (Physics.Raycast(ray, out var hit, maxDetectDistance, raycastMask))
            {
                if (hit.collider.CompareTag("Enemy") || hit.collider.CompareTag("Tank"))
                {
                    _isAimingEnemy = true;
                }
            }

            Debug.DrawRay(ray.origin, ray.direction * maxDetectDistance, _isAimingEnemy ? Color.red : Color.white);


            // 状态变化才更新颜色
            if (_isAimingEnemy && _lastState != CrosshairState.Enemy)
            {
                _crosshairImage.color = enemyColor;
                _lastState = CrosshairState.Enemy;
            }
            else if (!_isAimingEnemy && _lastState != CrosshairState.Neutral)
            {
                _crosshairImage.color = normalColor;
                _lastState = CrosshairState.Neutral;
            }
        }
    }
}